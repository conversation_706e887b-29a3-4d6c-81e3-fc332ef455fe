<center><div style="vertical-align:middle;"><a href="https://www.ankingmed.com"><img src="../../addons21/1210908941/AnKing/AnKingSmall.png"></a><a href="https://www.ankingmed.com"><img src="../../addons21/1210908941/AnKing/TheAnKing.png"></a></div></center>
 
<center><a href="https://www.facebook.com/ankingmed"><img src="../../addons21/1210908941/AnKing/Facebook.jpg"></a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://www.instagram.com/ankingmed"><img src="../../addons21/1210908941/AnKing/Instagram.jpg"></a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://www.youtube.com/theanking"><img src="../../addons21/1210908941/AnKing/YouTube.jpg"></a></center>
 
<center><a href="https://www.patreon.com/ankingmed"><img src="../../addons21/1210908941/AnKing/Patreon.jpg"></a></center>
 
---
 
<div style="color: #4297F9;"><b>如果您喜欢这个插件或者需要个性化的 Anki 帮助，请考虑支持我们！</b></div>
 
<div style="color: red; font-size: 16px; background-color: rgb(25, 25, 25); text-align: center;"><br>任何更改都需要重启 Anki 才能生效<br></div>
 
---
 
_<div style="color:#A440C3;">您可以将自定义图片添加到 "background" 和 "gears" 文件夹中，将自定义 CSS 添加到 "css" 文件夹中。通过 <b>工具->背景/齿轮图片文件夹</b> 访问此文件夹。我建议为背景使用不透明度为 20-50% 的图片（这必须在像 Photoshop 这样的外部编辑器中配置）。</div>_
 
<span style="color:grey;"><br><b>背景图片</b>获取自</span> [Pexels.com](https://www.pexels.com/photo-license/)
<span style="color:grey;"><br><b>齿轮图标</b>获取自</span> [Wikimedia Commons](https://commons.wikimedia.org/wiki/Category:Noto_Color_Emoji_Pie)
 
---
 
## 配置:
 
* **背景图片名称:** _(若不需要图片，请使用 ""。"Random" 将会随机显示默认图片)_ 背景图片文件的名称。 
    * _<span style="color:red;">预加载的图片 (均为 .png) 包括: AnKing, Beach, BeachAerial, Christ&Surgeon, ColorfulLights, Fire, Island, Milkyway, MoonMountains, NightSky, Ocean, SLCtemple, Sunset, SunsetMountain</div>_
* **齿轮图片名称:** _(Anki 默认是 "gears.svg"。"Random" 将会随机显示默认图片)_ 替换齿轮图标的文件名称。(预加载的图片包括 AnKing.png, flame.svg)
    * _<span style="color:red;">预加载的图片 (均为 .png) 包括: AnKing, Bam, Bullseye, Cowboy, Diamond, Dragon, Fire, Flower, Nerd, Rose, Shield, Skull, Star, Sun</div>_
* **复习器图片:** _(true 或 false)_ 在复习器界面显示背景图片
* **工具栏顶部/底部:** _(true 或 false)_ 将工具栏的背景位置设置为顶部和底部 (如果主背景位置设置为居中，这对大多数图片来说会更美观)
* **工具栏图片:** _(true 或 false)_ 除了主屏幕外，在顶部和底部工具栏中也显示背景图片
 
 
_以下是 CSS 值，可以使用相应的 CSS 属性值进行样式设置。更多信息请参阅 w3schools.com。_
 
* **background-attachment:** _(默认为 "fixed")_ scroll (滚动) 或 fixed (固定)
* **background-color:** _(默认为 "")_ 设置为 "" 则没有背景颜色
* **background-position:** _(默认为 "center")_ left top, right bottom, 25% 50%, 100px 200px, 等
* **background scale:** _(默认为 "1")_ 设置图片的缩放比例。1 是原始图片大小。2 是原始图片大小的 200%。您也可以使用 2,1 分别缩放 x,y 轴
* **background-size:** _(默认为 "contain", 但如果您将 "工具栏图片" 设置为 true，我建议使用 "cover")_ contain, cover, 50%, 100px, 等
