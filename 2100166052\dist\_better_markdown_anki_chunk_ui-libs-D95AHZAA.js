import{r as p,R as yn,a as gn}from"./_better_markdown_anki_chunk_react-vendor-Bkk_KtsK.js";var Ke={exports:{}},xe={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zt;function vn(){if(zt)return xe;zt=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(n,o,s){var a=null;if(s!==void 0&&(a=""+s),o.key!==void 0&&(a=""+o.key),"key"in o){s={};for(var i in o)i!=="key"&&(s[i]=o[i])}else s=o;return o=s.ref,{$$typeof:e,type:n,key:a,ref:o!==void 0?o:null,props:s}}return xe.Fragment=t,xe.jsx=r,xe.jsxs=r,xe}var Lt;function Sn(){return Lt||(Lt=1,Ke.exports=vn()),Ke.exports}var d=Sn();function Z(e){return Object.keys(e)}function et(e){return e&&typeof e=="object"&&!Array.isArray(e)}function pt(e,t){const r={...e},n=t;return et(e)&&et(t)&&Object.keys(t).forEach(o=>{et(n[o])&&o in e?r[o]=pt(r[o],n[o]):r[o]=n[o]}),r}function wn(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}function xn(e){return typeof e!="string"||!e.includes("var(--mantine-scale)")?e:e.match(/^calc\((.*?)\)$/)?.[1].split("*")[0].trim()}function $n(e){const t=xn(e);return typeof t=="number"?t:typeof t=="string"?t.includes("calc")||t.includes("var")?t:t.includes("px")?Number(t.replace("px","")):t.includes("rem")?Number(t.replace("rem",""))*16:t.includes("em")?Number(t.replace("em",""))*16:Number(t):NaN}function Bt(e){return e==="0rem"?"0rem":`calc(${e} * var(--mantine-scale))`}function or(e,{shouldScale:t=!1}={}){function r(n){if(n===0||n==="0")return`0${e}`;if(typeof n=="number"){const o=`${n/16}${e}`;return t?Bt(o):o}if(typeof n=="string"){if(n===""||n.startsWith("calc(")||n.startsWith("clamp(")||n.includes("rgba("))return n;if(n.includes(","))return n.split(",").map(s=>r(s)).join(",");if(n.includes(" "))return n.split(" ").map(s=>r(s)).join(" ");const o=n.replace("px","");if(!Number.isNaN(Number(o))){const s=`${Number(o)/16}${e}`;return t?Bt(s):s}}return n}return r}const b=or("rem",{shouldScale:!0}),Wt=or("em");function mt(e){return Object.keys(e).reduce((t,r)=>(e[r]!==void 0&&(t[r]=e[r]),t),{})}function sr(e){if(typeof e=="number")return!0;if(typeof e=="string"){if(e.startsWith("calc(")||e.startsWith("var(")||e.includes(" ")&&e.trim()!=="")return!0;const t=/^[+-]?[0-9]+(\.[0-9]+)?(px|em|rem|ex|ch|lh|rlh|vw|vh|vmin|vmax|vb|vi|svw|svh|lvw|lvh|dvw|dvh|cm|mm|in|pt|pc|q|cqw|cqh|cqi|cqb|cqmin|cqmax|%)?$/;return e.trim().split(/\s+/).every(n=>t.test(n))}return!1}function Le(e){const t=p.createContext(null);return[({children:o,value:s})=>d.jsx(t.Provider,{value:s,children:o}),()=>{const o=p.useContext(t);if(o===null)throw new Error(e);return o}]}function ar(e=null){const t=p.createContext(e);return[({children:o,value:s})=>d.jsx(t.Provider,{value:s,children:o}),()=>p.useContext(t)]}const Cn=()=>{};function A(e,t="size",r=!0){if(e!==void 0)return sr(e)?r?b(e):e:`var(--${t}-${e})`}function be(e){return A(e,"mantine-spacing")}function de(e){return e===void 0?"var(--mantine-radius-default)":A(e,"mantine-radius")}function V(e){return A(e,"mantine-font-size")}function Rn(e){return A(e,"mantine-line-height",!1)}function Pn(e){if(e)return A(e,"mantine-shadow",!1)}function Tn(e="mantine-"){return`${e}${Math.random().toString(36).slice(2,11)}`}function ce(e){const t=p.useRef(e);return p.useEffect(()=>{t.current=e}),p.useMemo(()=>(...r)=>t.current?.(...r),[])}function Be(e,t){const{delay:r,flushOnUnmount:n,leading:o}=typeof t=="number"?{delay:t,flushOnUnmount:!1,leading:!1}:t,s=ce(e),a=p.useRef(0),i=p.useMemo(()=>{const c=Object.assign((...l)=>{window.clearTimeout(a.current);const u=c._isFirstCall;if(c._isFirstCall=!1,o&&u){s(...l);return}function f(){window.clearTimeout(a.current),a.current=0,c._isFirstCall=!0}const h=()=>{a.current!==0&&(f(),s(...l))},m=()=>{f()};c.flush=h,c.cancel=m,a.current=window.setTimeout(h,r)},{flush:()=>{},cancel:()=>{},_isFirstCall:!0});return c},[s,r,o]);return p.useEffect(()=>()=>{n?i.flush():i.cancel()},[i,n]),i}function _n(e,t){try{return e.addEventListener("change",t),()=>e.removeEventListener("change",t)}catch{return e.addListener(t),()=>e.removeListener(t)}}function jn(e,t){return typeof window<"u"&&"matchMedia"in window?window.matchMedia(e).matches:!1}function ir(e,t,{getInitialValueInEffect:r}={getInitialValueInEffect:!0}){const[n,o]=p.useState(r?t:jn(e)),s=p.useRef(null);return p.useEffect(()=>{if("matchMedia"in window)return s.current=window.matchMedia(e),o(s.current.matches),_n(s.current,a=>o(a.matches))},[e]),n||!1}function Nn(e,t){return ir("(prefers-color-scheme: dark)",e==="dark",t)?"dark":"light"}const We=typeof document<"u"?p.useLayoutEffect:p.useEffect;function kn(e,t){const r=p.useRef(!1);p.useEffect(()=>()=>{r.current=!1},[]),p.useEffect(()=>{if(r.current)return e();r.current=!0},t)}const En=yn.useId||(()=>{});function An(){const e=En();return e?`mantine-${e.replace(/:/g,"")}`:""}function cr(e){const t=An(),[r,n]=p.useState(t);return We(()=>{n(Tn())},[]),typeof e=="string"?e:typeof window>"u"?t:r}function Ht(e,t){if(typeof e=="function")return e(t);typeof e=="object"&&e!==null&&"current"in e&&(e.current=t)}function In(...e){const t=new Map;return r=>{if(e.forEach(n=>{const o=Ht(n,r);o&&t.set(n,o)}),t.size>0)return()=>{e.forEach(n=>{const o=t.get(n);o?o():Ht(n,null)}),t.clear()}}}function ge(...e){return p.useCallback(In(...e),e)}function lr({value:e,defaultValue:t,finalValue:r,onChange:n=()=>{}}){const[o,s]=p.useState(t!==void 0?t:r),a=(i,...c)=>{s(i),n?.(i,...c)};return e!==void 0?[e,n,!0]:[o,a,!1]}function Mn(e,t){return ir("(prefers-reduced-motion: reduce)",e,t)}function dr(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=dr(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ue(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=dr(e))&&(n&&(n+=" "),n+=t);return n}const zn={};function Ln(e){const t={};return e.forEach(r=>{Object.entries(r).forEach(([n,o])=>{t[n]?t[n]=ue(t[n],o):t[n]=o})}),t}function He({theme:e,classNames:t,props:r,stylesCtx:n}){const s=(Array.isArray(t)?t:[t]).map(a=>typeof a=="function"?a(e,r,n):a||zn);return Ln(s)}function Me({theme:e,styles:t,props:r,stylesCtx:n}){return(Array.isArray(t)?t:[t]).reduce((s,a)=>typeof a=="function"?{...s,...a(e,r,n)}:{...s,...a},{})}const ht=p.createContext(null);function oe(){const e=p.useContext(ht);if(!e)throw new Error("[@mantine/core] MantineProvider was not found in tree");return e}function Bn(){return oe().cssVariablesResolver}function Wn(){return oe().classNamesPrefix}function De(){return oe().getStyleNonce}function Hn(){return oe().withStaticClasses}function Dn(){return oe().headless}function On(){return oe().stylesTransform?.sx}function Fn(){return oe().stylesTransform?.styles}function Vn(){return oe().env||"default"}function Yn(e){return/^#?([0-9A-F]{3}){1,2}([0-9A-F]{2})?$/i.test(e)}function Gn(e){let t=e.replace("#","");if(t.length===3){const a=t.split("");t=[a[0],a[0],a[1],a[1],a[2],a[2]].join("")}if(t.length===8){const a=parseInt(t.slice(6,8),16)/255;return{r:parseInt(t.slice(0,2),16),g:parseInt(t.slice(2,4),16),b:parseInt(t.slice(4,6),16),a}}const r=parseInt(t,16),n=r>>16&255,o=r>>8&255,s=r&255;return{r:n,g:o,b:s,a:1}}function Un(e){const[t,r,n,o]=e.replace(/[^0-9,./]/g,"").split(/[/,]/).map(Number);return{r:t,g:r,b:n,a:o===void 0?1:o}}function Xn(e){const t=/^hsla?\(\s*(\d+)\s*,\s*(\d+%)\s*,\s*(\d+%)\s*(,\s*(0?\.\d+|\d+(\.\d+)?))?\s*\)$/i,r=e.match(t);if(!r)return{r:0,g:0,b:0,a:1};const n=parseInt(r[1],10),o=parseInt(r[2],10)/100,s=parseInt(r[3],10)/100,a=r[5]?parseFloat(r[5]):void 0,i=(1-Math.abs(2*s-1))*o,c=n/60,l=i*(1-Math.abs(c%2-1)),u=s-i/2;let f,h,m;return c>=0&&c<1?(f=i,h=l,m=0):c>=1&&c<2?(f=l,h=i,m=0):c>=2&&c<3?(f=0,h=i,m=l):c>=3&&c<4?(f=0,h=l,m=i):c>=4&&c<5?(f=l,h=0,m=i):(f=i,h=0,m=l),{r:Math.round((f+u)*255),g:Math.round((h+u)*255),b:Math.round((m+u)*255),a:a||1}}function bt(e){return Yn(e)?Gn(e):e.startsWith("rgb")?Un(e):e.startsWith("hsl")?Xn(e):{r:0,g:0,b:0,a:1}}function ke(e,t){if(e.startsWith("var("))return`color-mix(in srgb, ${e}, black ${t*100}%)`;const{r,g:n,b:o,a:s}=bt(e),a=1-t,i=c=>Math.round(c*a);return`rgba(${i(r)}, ${i(n)}, ${i(o)}, ${s})`}function Pe(e,t){return typeof e.primaryShade=="number"?e.primaryShade:t==="dark"?e.primaryShade.dark:e.primaryShade.light}function tt(e){return e<=.03928?e/12.92:((e+.055)/1.055)**2.4}function qn(e){const t=e.match(/oklch\((.*?)%\s/);return t?parseFloat(t[1]):null}function Jn(e){if(e.startsWith("oklch("))return(qn(e)||0)/100;const{r:t,g:r,b:n}=bt(e),o=t/255,s=r/255,a=n/255,i=tt(o),c=tt(s),l=tt(a);return .2126*i+.7152*c+.0722*l}function $e(e,t=.179){return e.startsWith("var(")?!1:Jn(e)>t}function Te({color:e,theme:t,colorScheme:r}){if(typeof e!="string")throw new Error(`[@mantine/core] Failed to parse color. Expected color to be a string, instead got ${typeof e}`);if(e==="bright")return{color:e,value:r==="dark"?t.white:t.black,shade:void 0,isThemeColor:!1,isLight:$e(r==="dark"?t.white:t.black,t.luminanceThreshold),variable:"--mantine-color-bright"};if(e==="dimmed")return{color:e,value:r==="dark"?t.colors.dark[2]:t.colors.gray[7],shade:void 0,isThemeColor:!1,isLight:$e(r==="dark"?t.colors.dark[2]:t.colors.gray[6],t.luminanceThreshold),variable:"--mantine-color-dimmed"};if(e==="white"||e==="black")return{color:e,value:e==="white"?t.white:t.black,shade:void 0,isThemeColor:!1,isLight:$e(e==="white"?t.white:t.black,t.luminanceThreshold),variable:`--mantine-color-${e}`};const[n,o]=e.split("."),s=o?Number(o):void 0,a=n in t.colors;if(a){const i=s!==void 0?t.colors[n][s]:t.colors[n][Pe(t,r||"light")];return{color:n,value:i,shade:s,isThemeColor:a,isLight:$e(i,t.luminanceThreshold),variable:o?`--mantine-color-${n}-${s}`:`--mantine-color-${n}-filled`}}return{color:e,value:e,isThemeColor:a,isLight:$e(e,t.luminanceThreshold),shade:s,variable:void 0}}function te(e,t){const r=Te({color:e||t.primaryColor,theme:t});return r.variable?`var(${r.variable})`:e}function it(e,t){const r={from:e?.from||t.defaultGradient.from,to:e?.to||t.defaultGradient.to,deg:e?.deg??t.defaultGradient.deg??0},n=te(r.from,t),o=te(r.to,t);return`linear-gradient(${r.deg}deg, ${n} 0%, ${o} 100%)`}function ee(e,t){if(typeof e!="string"||t>1||t<0)return"rgba(0, 0, 0, 1)";if(e.startsWith("var(")){const s=(1-t)*100;return`color-mix(in srgb, ${e}, transparent ${s}%)`}if(e.startsWith("oklch"))return e.includes("/")?e.replace(/\/\s*[\d.]+\s*\)/,`/ ${t})`):e.replace(")",` / ${t})`);const{r,g:n,b:o}=bt(e);return`rgba(${r}, ${n}, ${o}, ${t})`}const pe=ee,Qn=({color:e,theme:t,variant:r,gradient:n,autoContrast:o})=>{const s=Te({color:e,theme:t}),a=typeof o=="boolean"?o:t.autoContrast;if(r==="none")return{background:"transparent",hover:"transparent",color:"inherit",border:"none"};if(r==="filled"){const i=a&&s.isLight?"var(--mantine-color-black)":"var(--mantine-color-white)";return s.isThemeColor?s.shade===void 0?{background:`var(--mantine-color-${e}-filled)`,hover:`var(--mantine-color-${e}-filled-hover)`,color:i,border:`${b(1)} solid transparent`}:{background:`var(--mantine-color-${s.color}-${s.shade})`,hover:`var(--mantine-color-${s.color}-${s.shade===9?8:s.shade+1})`,color:i,border:`${b(1)} solid transparent`}:{background:e,hover:ke(e,.1),color:i,border:`${b(1)} solid transparent`}}if(r==="light"){if(s.isThemeColor){if(s.shade===void 0)return{background:`var(--mantine-color-${e}-light)`,hover:`var(--mantine-color-${e}-light-hover)`,color:`var(--mantine-color-${e}-light-color)`,border:`${b(1)} solid transparent`};const i=t.colors[s.color][s.shade];return{background:ee(i,.1),hover:ee(i,.12),color:`var(--mantine-color-${s.color}-${Math.min(s.shade,6)})`,border:`${b(1)} solid transparent`}}return{background:ee(e,.1),hover:ee(e,.12),color:e,border:`${b(1)} solid transparent`}}if(r==="outline")return s.isThemeColor?s.shade===void 0?{background:"transparent",hover:`var(--mantine-color-${e}-outline-hover)`,color:`var(--mantine-color-${e}-outline)`,border:`${b(1)} solid var(--mantine-color-${e}-outline)`}:{background:"transparent",hover:ee(t.colors[s.color][s.shade],.05),color:`var(--mantine-color-${s.color}-${s.shade})`,border:`${b(1)} solid var(--mantine-color-${s.color}-${s.shade})`}:{background:"transparent",hover:ee(e,.05),color:e,border:`${b(1)} solid ${e}`};if(r==="subtle"){if(s.isThemeColor){if(s.shade===void 0)return{background:"transparent",hover:`var(--mantine-color-${e}-light-hover)`,color:`var(--mantine-color-${e}-light-color)`,border:`${b(1)} solid transparent`};const i=t.colors[s.color][s.shade];return{background:"transparent",hover:ee(i,.12),color:`var(--mantine-color-${s.color}-${Math.min(s.shade,6)})`,border:`${b(1)} solid transparent`}}return{background:"transparent",hover:ee(e,.12),color:e,border:`${b(1)} solid transparent`}}return r==="transparent"?s.isThemeColor?s.shade===void 0?{background:"transparent",hover:"transparent",color:`var(--mantine-color-${e}-light-color)`,border:`${b(1)} solid transparent`}:{background:"transparent",hover:"transparent",color:`var(--mantine-color-${s.color}-${Math.min(s.shade,6)})`,border:`${b(1)} solid transparent`}:{background:"transparent",hover:"transparent",color:e,border:`${b(1)} solid transparent`}:r==="white"?s.isThemeColor?s.shade===void 0?{background:"var(--mantine-color-white)",hover:ke(t.white,.01),color:`var(--mantine-color-${e}-filled)`,border:`${b(1)} solid transparent`}:{background:"var(--mantine-color-white)",hover:ke(t.white,.01),color:`var(--mantine-color-${s.color}-${s.shade})`,border:`${b(1)} solid transparent`}:{background:"var(--mantine-color-white)",hover:ke(t.white,.01),color:e,border:`${b(1)} solid transparent`}:r==="gradient"?{background:it(n,t),hover:it(n,t),color:"var(--mantine-color-white)",border:"none"}:r==="default"?{background:"var(--mantine-color-default)",hover:"var(--mantine-color-default-hover)",color:"var(--mantine-color-default-color)",border:`${b(1)} solid var(--mantine-color-default-border)`}:{}},Zn={dark:["#C9C9C9","#b8b8b8","#828282","#696969","#424242","#3b3b3b","#2e2e2e","#242424","#1f1f1f","#141414"],gray:["#f8f9fa","#f1f3f5","#e9ecef","#dee2e6","#ced4da","#adb5bd","#868e96","#495057","#343a40","#212529"],red:["#fff5f5","#ffe3e3","#ffc9c9","#ffa8a8","#ff8787","#ff6b6b","#fa5252","#f03e3e","#e03131","#c92a2a"],pink:["#fff0f6","#ffdeeb","#fcc2d7","#faa2c1","#f783ac","#f06595","#e64980","#d6336c","#c2255c","#a61e4d"],grape:["#f8f0fc","#f3d9fa","#eebefa","#e599f7","#da77f2","#cc5de8","#be4bdb","#ae3ec9","#9c36b5","#862e9c"],violet:["#f3f0ff","#e5dbff","#d0bfff","#b197fc","#9775fa","#845ef7","#7950f2","#7048e8","#6741d9","#5f3dc4"],indigo:["#edf2ff","#dbe4ff","#bac8ff","#91a7ff","#748ffc","#5c7cfa","#4c6ef5","#4263eb","#3b5bdb","#364fc7"],blue:["#e7f5ff","#d0ebff","#a5d8ff","#74c0fc","#4dabf7","#339af0","#228be6","#1c7ed6","#1971c2","#1864ab"],cyan:["#e3fafc","#c5f6fa","#99e9f2","#66d9e8","#3bc9db","#22b8cf","#15aabf","#1098ad","#0c8599","#0b7285"],teal:["#e6fcf5","#c3fae8","#96f2d7","#63e6be","#38d9a9","#20c997","#12b886","#0ca678","#099268","#087f5b"],green:["#ebfbee","#d3f9d8","#b2f2bb","#8ce99a","#69db7c","#51cf66","#40c057","#37b24d","#2f9e44","#2b8a3e"],lime:["#f4fce3","#e9fac8","#d8f5a2","#c0eb75","#a9e34b","#94d82d","#82c91e","#74b816","#66a80f","#5c940d"],yellow:["#fff9db","#fff3bf","#ffec99","#ffe066","#ffd43b","#fcc419","#fab005","#f59f00","#f08c00","#e67700"],orange:["#fff4e6","#ffe8cc","#ffd8a8","#ffc078","#ffa94d","#ff922b","#fd7e14","#f76707","#e8590c","#d9480f"]},Dt="-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji",yt={scale:1,fontSmoothing:!0,focusRing:"auto",white:"#fff",black:"#000",colors:Zn,primaryShade:{light:6,dark:8},primaryColor:"blue",variantColorResolver:Qn,autoContrast:!1,luminanceThreshold:.3,fontFamily:Dt,fontFamilyMonospace:"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace",respectReducedMotion:!1,cursorType:"default",defaultGradient:{from:"blue",to:"cyan",deg:45},defaultRadius:"sm",activeClassName:"mantine-active",focusClassName:"",headings:{fontFamily:Dt,fontWeight:"700",textWrap:"wrap",sizes:{h1:{fontSize:b(34),lineHeight:"1.3"},h2:{fontSize:b(26),lineHeight:"1.35"},h3:{fontSize:b(22),lineHeight:"1.4"},h4:{fontSize:b(18),lineHeight:"1.45"},h5:{fontSize:b(16),lineHeight:"1.5"},h6:{fontSize:b(14),lineHeight:"1.5"}}},fontSizes:{xs:b(12),sm:b(14),md:b(16),lg:b(18),xl:b(20)},lineHeights:{xs:"1.4",sm:"1.45",md:"1.55",lg:"1.6",xl:"1.65"},radius:{xs:b(2),sm:b(4),md:b(8),lg:b(16),xl:b(32)},spacing:{xs:b(10),sm:b(12),md:b(16),lg:b(20),xl:b(32)},breakpoints:{xs:"36em",sm:"48em",md:"62em",lg:"75em",xl:"88em"},shadows:{xs:`0 ${b(1)} ${b(3)} rgba(0, 0, 0, 0.05), 0 ${b(1)} ${b(2)} rgba(0, 0, 0, 0.1)`,sm:`0 ${b(1)} ${b(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${b(10)} ${b(15)} ${b(-5)}, rgba(0, 0, 0, 0.04) 0 ${b(7)} ${b(7)} ${b(-5)}`,md:`0 ${b(1)} ${b(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${b(20)} ${b(25)} ${b(-5)}, rgba(0, 0, 0, 0.04) 0 ${b(10)} ${b(10)} ${b(-5)}`,lg:`0 ${b(1)} ${b(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${b(28)} ${b(23)} ${b(-7)}, rgba(0, 0, 0, 0.04) 0 ${b(12)} ${b(12)} ${b(-7)}`,xl:`0 ${b(1)} ${b(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${b(36)} ${b(28)} ${b(-7)}, rgba(0, 0, 0, 0.04) 0 ${b(17)} ${b(17)} ${b(-7)}`},other:{},components:{}};function Ot(e){return e==="auto"||e==="dark"||e==="light"}function Kn({key:e="mantine-color-scheme-value"}={}){let t;return{get:r=>{if(typeof window>"u")return r;try{const n=window.localStorage.getItem(e);return Ot(n)?n:r}catch{return r}},set:r=>{try{window.localStorage.setItem(e,r)}catch(n){console.warn("[@mantine/core] Local storage color scheme manager was unable to save color scheme.",n)}},subscribe:r=>{t=n=>{n.storageArea===window.localStorage&&n.key===e&&Ot(n.newValue)&&r(n.newValue)},window.addEventListener("storage",t)},unsubscribe:()=>{window.removeEventListener("storage",t)},clear:()=>{window.localStorage.removeItem(e)}}}const eo="[@mantine/core] MantineProvider: Invalid theme.primaryColor, it accepts only key of theme.colors, learn more – https://mantine.dev/theming/colors/#primary-color",Ft="[@mantine/core] MantineProvider: Invalid theme.primaryShade, it accepts only 0-9 integers or an object { light: 0-9, dark: 0-9 }";function rt(e){return e<0||e>9?!1:parseInt(e.toString(),10)===e}function Vt(e){if(!(e.primaryColor in e.colors))throw new Error(eo);if(typeof e.primaryShade=="object"&&(!rt(e.primaryShade.dark)||!rt(e.primaryShade.light)))throw new Error(Ft);if(typeof e.primaryShade=="number"&&!rt(e.primaryShade))throw new Error(Ft)}function to(e,t){if(!t)return Vt(e),e;const r=pt(e,t);return t.fontFamily&&!t.headings?.fontFamily&&(r.headings.fontFamily=t.fontFamily),Vt(r),r}const gt=p.createContext(null),ro=()=>p.useContext(gt)||yt;function se(){const e=p.useContext(gt);if(!e)throw new Error("@mantine/core: MantineProvider was not found in component tree, make sure you have it in your app");return e}function ur({theme:e,children:t,inherit:r=!0}){const n=ro(),o=p.useMemo(()=>to(r?n:yt,e),[e,n,r]);return d.jsx(gt.Provider,{value:o,children:t})}ur.displayName="@mantine/core/MantineThemeProvider";function no(){const e=se(),t=De(),r=Z(e.breakpoints).reduce((n,o)=>{const s=e.breakpoints[o].includes("px"),a=$n(e.breakpoints[o]),i=s?`${a-.1}px`:Wt(a-.1),c=s?`${a}px`:Wt(a);return`${n}@media (max-width: ${i}) {.mantine-visible-from-${o} {display: none !important;}}@media (min-width: ${c}) {.mantine-hidden-from-${o} {display: none !important;}}`},"");return d.jsx("style",{"data-mantine-styles":"classes",nonce:t?.(),dangerouslySetInnerHTML:{__html:r}})}function nt(e){return Object.entries(e).map(([t,r])=>`${t}: ${r};`).join("")}function Ce(e,t){return(Array.isArray(e)?e:[e]).reduce((n,o)=>`${o}{${n}}`,t)}function oo(e,t){const r=nt(e.variables),n=r?Ce(t,r):"",o=nt(e.dark),s=nt(e.light),a=o?Ce(t===":host"?`${t}([data-mantine-color-scheme="dark"])`:`${t}[data-mantine-color-scheme="dark"]`,o):"",i=s?Ce(t===":host"?`${t}([data-mantine-color-scheme="light"])`:`${t}[data-mantine-color-scheme="light"]`,s):"";return`${n}${a}${i}`}function so({color:e,theme:t,autoContrast:r}){return t.autoContrast&&Te({color:e||t.primaryColor,theme:t}).isLight?"var(--mantine-color-black)":"var(--mantine-color-white)"}function Yt(e,t){return so({color:e.colors[e.primaryColor][Pe(e,t)],theme:e,autoContrast:null})}function Ee({theme:e,color:t,colorScheme:r,name:n=t,withColorValues:o=!0}){if(!e.colors[t])return{};if(r==="light"){const i=Pe(e,"light"),c={[`--mantine-color-${n}-text`]:`var(--mantine-color-${n}-filled)`,[`--mantine-color-${n}-filled`]:`var(--mantine-color-${n}-${i})`,[`--mantine-color-${n}-filled-hover`]:`var(--mantine-color-${n}-${i===9?8:i+1})`,[`--mantine-color-${n}-light`]:pe(e.colors[t][i],.1),[`--mantine-color-${n}-light-hover`]:pe(e.colors[t][i],.12),[`--mantine-color-${n}-light-color`]:`var(--mantine-color-${n}-${i})`,[`--mantine-color-${n}-outline`]:`var(--mantine-color-${n}-${i})`,[`--mantine-color-${n}-outline-hover`]:pe(e.colors[t][i],.05)};return o?{[`--mantine-color-${n}-0`]:e.colors[t][0],[`--mantine-color-${n}-1`]:e.colors[t][1],[`--mantine-color-${n}-2`]:e.colors[t][2],[`--mantine-color-${n}-3`]:e.colors[t][3],[`--mantine-color-${n}-4`]:e.colors[t][4],[`--mantine-color-${n}-5`]:e.colors[t][5],[`--mantine-color-${n}-6`]:e.colors[t][6],[`--mantine-color-${n}-7`]:e.colors[t][7],[`--mantine-color-${n}-8`]:e.colors[t][8],[`--mantine-color-${n}-9`]:e.colors[t][9],...c}:c}const s=Pe(e,"dark"),a={[`--mantine-color-${n}-text`]:`var(--mantine-color-${n}-4)`,[`--mantine-color-${n}-filled`]:`var(--mantine-color-${n}-${s})`,[`--mantine-color-${n}-filled-hover`]:`var(--mantine-color-${n}-${s===9?8:s+1})`,[`--mantine-color-${n}-light`]:pe(e.colors[t][Math.max(0,s-2)],.15),[`--mantine-color-${n}-light-hover`]:pe(e.colors[t][Math.max(0,s-2)],.2),[`--mantine-color-${n}-light-color`]:`var(--mantine-color-${n}-${Math.max(s-5,0)})`,[`--mantine-color-${n}-outline`]:`var(--mantine-color-${n}-${Math.max(s-4,0)})`,[`--mantine-color-${n}-outline-hover`]:pe(e.colors[t][Math.max(s-4,0)],.05)};return o?{[`--mantine-color-${n}-0`]:e.colors[t][0],[`--mantine-color-${n}-1`]:e.colors[t][1],[`--mantine-color-${n}-2`]:e.colors[t][2],[`--mantine-color-${n}-3`]:e.colors[t][3],[`--mantine-color-${n}-4`]:e.colors[t][4],[`--mantine-color-${n}-5`]:e.colors[t][5],[`--mantine-color-${n}-6`]:e.colors[t][6],[`--mantine-color-${n}-7`]:e.colors[t][7],[`--mantine-color-${n}-8`]:e.colors[t][8],[`--mantine-color-${n}-9`]:e.colors[t][9],...a}:a}function ao(e){return!!e&&typeof e=="object"&&"mantine-virtual-color"in e}function me(e,t,r){Z(t).forEach(n=>Object.assign(e,{[`--mantine-${r}-${n}`]:t[n]}))}const fr=e=>{const t=Pe(e,"light"),r=e.defaultRadius in e.radius?e.radius[e.defaultRadius]:b(e.defaultRadius),n={variables:{"--mantine-scale":e.scale.toString(),"--mantine-cursor-type":e.cursorType,"--mantine-color-scheme":"light dark","--mantine-webkit-font-smoothing":e.fontSmoothing?"antialiased":"unset","--mantine-moz-font-smoothing":e.fontSmoothing?"grayscale":"unset","--mantine-color-white":e.white,"--mantine-color-black":e.black,"--mantine-line-height":e.lineHeights.md,"--mantine-font-family":e.fontFamily,"--mantine-font-family-monospace":e.fontFamilyMonospace,"--mantine-font-family-headings":e.headings.fontFamily,"--mantine-heading-font-weight":e.headings.fontWeight,"--mantine-heading-text-wrap":e.headings.textWrap,"--mantine-radius-default":r,"--mantine-primary-color-filled":`var(--mantine-color-${e.primaryColor}-filled)`,"--mantine-primary-color-filled-hover":`var(--mantine-color-${e.primaryColor}-filled-hover)`,"--mantine-primary-color-light":`var(--mantine-color-${e.primaryColor}-light)`,"--mantine-primary-color-light-hover":`var(--mantine-color-${e.primaryColor}-light-hover)`,"--mantine-primary-color-light-color":`var(--mantine-color-${e.primaryColor}-light-color)`},light:{"--mantine-primary-color-contrast":Yt(e,"light"),"--mantine-color-bright":"var(--mantine-color-black)","--mantine-color-text":e.black,"--mantine-color-body":e.white,"--mantine-color-error":"var(--mantine-color-red-6)","--mantine-color-placeholder":"var(--mantine-color-gray-5)","--mantine-color-anchor":`var(--mantine-color-${e.primaryColor}-${t})`,"--mantine-color-default":"var(--mantine-color-white)","--mantine-color-default-hover":"var(--mantine-color-gray-0)","--mantine-color-default-color":"var(--mantine-color-black)","--mantine-color-default-border":"var(--mantine-color-gray-4)","--mantine-color-dimmed":"var(--mantine-color-gray-6)","--mantine-color-disabled":"var(--mantine-color-gray-2)","--mantine-color-disabled-color":"var(--mantine-color-gray-5)","--mantine-color-disabled-border":"var(--mantine-color-gray-3)"},dark:{"--mantine-primary-color-contrast":Yt(e,"dark"),"--mantine-color-bright":"var(--mantine-color-white)","--mantine-color-text":"var(--mantine-color-dark-0)","--mantine-color-body":"var(--mantine-color-dark-7)","--mantine-color-error":"var(--mantine-color-red-8)","--mantine-color-placeholder":"var(--mantine-color-dark-3)","--mantine-color-anchor":`var(--mantine-color-${e.primaryColor}-4)`,"--mantine-color-default":"var(--mantine-color-dark-6)","--mantine-color-default-hover":"var(--mantine-color-dark-5)","--mantine-color-default-color":"var(--mantine-color-white)","--mantine-color-default-border":"var(--mantine-color-dark-4)","--mantine-color-dimmed":"var(--mantine-color-dark-2)","--mantine-color-disabled":"var(--mantine-color-dark-6)","--mantine-color-disabled-color":"var(--mantine-color-dark-3)","--mantine-color-disabled-border":"var(--mantine-color-gray-6)"}};me(n.variables,e.breakpoints,"breakpoint"),me(n.variables,e.spacing,"spacing"),me(n.variables,e.fontSizes,"font-size"),me(n.variables,e.lineHeights,"line-height"),me(n.variables,e.shadows,"shadow"),me(n.variables,e.radius,"radius"),e.colors[e.primaryColor].forEach((s,a)=>{n.variables[`--mantine-primary-color-${a}`]=`var(--mantine-color-${e.primaryColor}-${a})`}),Z(e.colors).forEach(s=>{const a=e.colors[s];if(ao(a)){Object.assign(n.light,Ee({theme:e,name:a.name,color:a.light,colorScheme:"light",withColorValues:!0})),Object.assign(n.dark,Ee({theme:e,name:a.name,color:a.dark,colorScheme:"dark",withColorValues:!0}));return}a.forEach((i,c)=>{n.variables[`--mantine-color-${s}-${c}`]=i}),Object.assign(n.light,Ee({theme:e,color:s,colorScheme:"light",withColorValues:!1})),Object.assign(n.dark,Ee({theme:e,color:s,colorScheme:"dark",withColorValues:!1}))});const o=e.headings.sizes;return Z(o).forEach(s=>{n.variables[`--mantine-${s}-font-size`]=o[s].fontSize,n.variables[`--mantine-${s}-line-height`]=o[s].lineHeight,n.variables[`--mantine-${s}-font-weight`]=o[s].fontWeight||e.headings.fontWeight}),n};function io({theme:e,generator:t}){const r=fr(e),n=t?.(e);return n?pt(r,n):r}const ot=fr(yt);function co(e){const t={variables:{},light:{},dark:{}};return Z(e.variables).forEach(r=>{ot.variables[r]!==e.variables[r]&&(t.variables[r]=e.variables[r])}),Z(e.light).forEach(r=>{ot.light[r]!==e.light[r]&&(t.light[r]=e.light[r])}),Z(e.dark).forEach(r=>{ot.dark[r]!==e.dark[r]&&(t.dark[r]=e.dark[r])}),t}function lo(e){return`
  ${e}[data-mantine-color-scheme="dark"] { --mantine-color-scheme: dark; }
  ${e}[data-mantine-color-scheme="light"] { --mantine-color-scheme: light; }
`}function pr({cssVariablesSelector:e,deduplicateCssVariables:t}){const r=se(),n=De(),o=Bn(),s=io({theme:r,generator:o}),a=e===":root"&&t,i=a?co(s):s,c=oo(i,e);return c?d.jsx("style",{"data-mantine-styles":!0,nonce:n?.(),dangerouslySetInnerHTML:{__html:`${c}${a?"":lo(e)}`}}):null}pr.displayName="@mantine/CssVariables";function he(e,t){const r=typeof window<"u"&&"matchMedia"in window&&window.matchMedia("(prefers-color-scheme: dark)")?.matches,n=e!=="auto"?e:r?"dark":"light";t()?.setAttribute("data-mantine-color-scheme",n)}function uo({manager:e,defaultColorScheme:t,getRootElement:r,forceColorScheme:n}){const o=p.useRef(null),[s,a]=p.useState(()=>e.get(t)),i=n||s,c=p.useCallback(u=>{n||(he(u,r),a(u),e.set(u))},[e.set,i,n]),l=p.useCallback(()=>{a(t),he(t,r),e.clear()},[e.clear,t]);return p.useEffect(()=>(e.subscribe(c),e.unsubscribe),[e.subscribe,e.unsubscribe]),We(()=>{he(e.get(t),r)},[]),p.useEffect(()=>{if(n)return he(n,r),()=>{};n===void 0&&he(s,r),typeof window<"u"&&"matchMedia"in window&&(o.current=window.matchMedia("(prefers-color-scheme: dark)"));const u=f=>{s==="auto"&&he(f.matches?"dark":"light",r)};return o.current?.addEventListener("change",u),()=>o.current?.removeEventListener("change",u)},[s,n]),{colorScheme:i,setColorScheme:c,clearColorScheme:l}}function fo({respectReducedMotion:e,getRootElement:t}){We(()=>{e&&t()?.setAttribute("data-respect-reduced-motion","true")},[e])}function po({theme:e,children:t,getStyleNonce:r,withStaticClasses:n=!0,withGlobalClasses:o=!0,deduplicateCssVariables:s=!0,withCssVariables:a=!0,cssVariablesSelector:i=":root",classNamesPrefix:c="mantine",colorSchemeManager:l=Kn(),defaultColorScheme:u="light",getRootElement:f=()=>document.documentElement,cssVariablesResolver:h,forceColorScheme:m,stylesTransform:y,env:g}){const{colorScheme:v,setColorScheme:w,clearColorScheme:$}=uo({defaultColorScheme:u,forceColorScheme:m,manager:l,getRootElement:f});return fo({respectReducedMotion:e?.respectReducedMotion||!1,getRootElement:f}),d.jsx(ht.Provider,{value:{colorScheme:v,setColorScheme:w,clearColorScheme:$,getRootElement:f,classNamesPrefix:c,getStyleNonce:r,cssVariablesResolver:h,cssVariablesSelector:i,withStaticClasses:n,stylesTransform:y,env:g},children:d.jsxs(ur,{theme:e,children:[a&&d.jsx(pr,{cssVariablesSelector:i,deduplicateCssVariables:s}),o&&d.jsx(no,{}),t]})})}po.displayName="@mantine/core/MantineProvider";function mo({classNames:e,styles:t,props:r,stylesCtx:n}){const o=se();return{resolvedClassNames:He({theme:o,classNames:e,props:r,stylesCtx:n||void 0}),resolvedStyles:Me({theme:o,styles:t,props:r,stylesCtx:n||void 0})}}const ho={always:"mantine-focus-always",auto:"mantine-focus-auto",never:"mantine-focus-never"};function bo({theme:e,options:t,unstyled:r}){return ue(t?.focusable&&!r&&(e.focusClassName||ho[e.focusRing]),t?.active&&!r&&e.activeClassName)}function yo({selector:e,stylesCtx:t,options:r,props:n,theme:o}){return He({theme:o,classNames:r?.classNames,props:r?.props||n,stylesCtx:t})[e]}function Gt({selector:e,stylesCtx:t,theme:r,classNames:n,props:o}){return He({theme:r,classNames:n,props:o,stylesCtx:t})[e]}function go({rootSelector:e,selector:t,className:r}){return e===t?r:void 0}function vo({selector:e,classes:t,unstyled:r}){return r?void 0:t[e]}function So({themeName:e,classNamesPrefix:t,selector:r,withStaticClass:n}){return n===!1?[]:e.map(o=>`${t}-${o}-${r}`)}function wo({themeName:e,theme:t,selector:r,props:n,stylesCtx:o}){return e.map(s=>He({theme:t,classNames:t.components[s]?.classNames,props:n,stylesCtx:o})?.[r])}function xo({options:e,classes:t,selector:r,unstyled:n}){return e?.variant&&!n?t[`${r}--${e.variant}`]:void 0}function $o({theme:e,options:t,themeName:r,selector:n,classNamesPrefix:o,classNames:s,classes:a,unstyled:i,className:c,rootSelector:l,props:u,stylesCtx:f,withStaticClasses:h,headless:m,transformedStyles:y}){return ue(bo({theme:e,options:t,unstyled:i||m}),wo({theme:e,themeName:r,selector:n,props:u,stylesCtx:f}),xo({options:t,classes:a,selector:n,unstyled:i}),Gt({selector:n,stylesCtx:f,theme:e,classNames:s,props:u}),Gt({selector:n,stylesCtx:f,theme:e,classNames:y,props:u}),yo({selector:n,stylesCtx:f,options:t,props:u,theme:e}),go({rootSelector:l,selector:n,className:c}),vo({selector:n,classes:a,unstyled:i||m}),h&&!m&&So({themeName:r,classNamesPrefix:o,selector:n,withStaticClass:t?.withStaticClass}),t?.className)}function Co({theme:e,themeName:t,props:r,stylesCtx:n,selector:o}){return t.map(s=>Me({theme:e,styles:e.components[s]?.styles,props:r,stylesCtx:n})[o]).reduce((s,a)=>({...s,...a}),{})}function ct({style:e,theme:t}){return Array.isArray(e)?[...e].reduce((r,n)=>({...r,...ct({style:n,theme:t})}),{}):typeof e=="function"?e(t):e??{}}function Ro(e){return e.reduce((t,r)=>(r&&Object.keys(r).forEach(n=>{t[n]={...t[n],...mt(r[n])}}),t),{})}function Po({vars:e,varsResolver:t,theme:r,props:n,stylesCtx:o,selector:s,themeName:a,headless:i}){return Ro([i?{}:t?.(r,n,o),...a.map(c=>r.components?.[c]?.vars?.(r,n,o)),e?.(r,n,o)])?.[s]}function To({theme:e,themeName:t,selector:r,options:n,props:o,stylesCtx:s,rootSelector:a,styles:i,style:c,vars:l,varsResolver:u,headless:f,withStylesTransform:h}){return{...!h&&Co({theme:e,themeName:t,props:o,stylesCtx:s,selector:r}),...!h&&Me({theme:e,styles:i,props:o,stylesCtx:s})[r],...!h&&Me({theme:e,styles:n?.styles,props:n?.props||o,stylesCtx:s})[r],...Po({theme:e,props:o,stylesCtx:s,vars:l,varsResolver:u,selector:r,themeName:t,headless:f}),...a===r?ct({style:c,theme:e}):null,...ct({style:n?.style,theme:e})}}function _o({props:e,stylesCtx:t,themeName:r}){const n=se(),o=Fn()?.();return{getTransformedStyles:a=>o?[...a.map(c=>o(c,{props:e,theme:n,ctx:t})),...r.map(c=>o(n.components[c]?.styles,{props:e,theme:n,ctx:t}))].filter(Boolean):[],withStylesTransform:!!o}}function E({name:e,classes:t,props:r,stylesCtx:n,className:o,style:s,rootSelector:a="root",unstyled:i,classNames:c,styles:l,vars:u,varsResolver:f}){const h=se(),m=Wn(),y=Hn(),g=Dn(),v=(Array.isArray(e)?e:[e]).filter(x=>x),{withStylesTransform:w,getTransformedStyles:$}=_o({props:r,stylesCtx:n,themeName:v});return(x,R)=>({className:$o({theme:h,options:R,themeName:v,selector:x,classNamesPrefix:m,classNames:c,classes:t,unstyled:i,className:o,rootSelector:a,props:r,stylesCtx:n,withStaticClasses:y,headless:g,transformedStyles:$([R?.styles,l])}),style:To({theme:h,themeName:v,selector:x,options:R,props:r,stylesCtx:n,rootSelector:a,styles:l,style:s,vars:u,varsResolver:f,headless:g,withStylesTransform:w})})}function Ut(e){const t=document.createElement("style");return t.setAttribute("data-mantine-styles","inline"),t.innerHTML="*, *::before, *::after {transition: none !important;}",t.setAttribute("data-mantine-disable-transition","true"),e&&t.setAttribute("nonce",e),document.head.appendChild(t),()=>document.querySelectorAll("[data-mantine-disable-transition]").forEach(n=>n.remove())}function ba({keepTransitions:e}={}){const t=p.useRef(Cn),r=p.useRef(-1),n=p.useContext(ht),o=De(),s=p.useRef(o?.());if(!n)throw new Error("[@mantine/core] MantineProvider was not found in tree");const a=f=>{n.setColorScheme(f),t.current=e?()=>{}:Ut(s.current),window.clearTimeout(r.current),r.current=window.setTimeout(()=>{t.current?.()},10)},i=()=>{n.clearColorScheme(),t.current=e?()=>{}:Ut(s.current),window.clearTimeout(r.current),r.current=window.setTimeout(()=>{t.current?.()},10)},c=Nn("light",{getInitialValueInEffect:!1}),l=n.colorScheme==="auto"?c:n.colorScheme,u=p.useCallback(()=>a(l==="light"?"dark":"light"),[a,l]);return p.useEffect(()=>()=>{t.current?.(),window.clearTimeout(r.current)},[]),{colorScheme:n.colorScheme,setColorScheme:a,clearColorScheme:i,toggleColorScheme:u}}function j(e,t,r){const n=se(),o=n.components[e]?.defaultProps,s=typeof o=="function"?o(n):o;return{...t,...s,...mt(r)}}function st(e){return Z(e).reduce((t,r)=>e[r]!==void 0?`${t}${wn(r)}:${e[r]};`:t,"").trim()}function jo({selector:e,styles:t,media:r,container:n}){const o=t?st(t):"",s=Array.isArray(r)?r.map(i=>`@media${i.query}{${e}{${st(i.styles)}}}`):[],a=Array.isArray(n)?n.map(i=>`@container ${i.query}{${e}{${st(i.styles)}}}`):[];return`${o?`${e}{${o}}`:""}${s.join("")}${a.join("")}`.trim()}function No(e){const t=De();return d.jsx("style",{"data-mantine-styles":"inline",nonce:t?.(),dangerouslySetInnerHTML:{__html:jo(e)}})}function vt(e){const{m:t,mx:r,my:n,mt:o,mb:s,ml:a,mr:i,me:c,ms:l,p:u,px:f,py:h,pt:m,pb:y,pl:g,pr:v,pe:w,ps:$,bd:x,bdrs:R,bg:_,c:C,opacity:P,ff:S,fz:N,fw:k,lts:D,ta:I,lh:M,fs:B,tt:W,td:L,w:Y,miw:O,maw:G,h:H,mih:K,mah:ae,bgsz:F,bgp:Se,bgr:ne,bga:we,pos:ie,top:Qe,left:Ze,bottom:sn,right:an,inset:cn,display:ln,flex:dn,hiddenFrom:un,visibleFrom:fn,lightHidden:pn,darkHidden:mn,sx:hn,...bn}=e;return{styleProps:mt({m:t,mx:r,my:n,mt:o,mb:s,ml:a,mr:i,me:c,ms:l,p:u,px:f,py:h,pt:m,pb:y,pl:g,pr:v,pe:w,ps:$,bd:x,bg:_,c:C,opacity:P,ff:S,fz:N,fw:k,lts:D,ta:I,lh:M,fs:B,tt:W,td:L,w:Y,miw:O,maw:G,h:H,mih:K,mah:ae,bgsz:F,bgp:Se,bgr:ne,bga:we,pos:ie,top:Qe,left:Ze,bottom:sn,right:an,inset:cn,display:ln,flex:dn,bdrs:R,hiddenFrom:un,visibleFrom:fn,lightHidden:pn,darkHidden:mn,sx:hn}),rest:bn}}const ko={m:{type:"spacing",property:"margin"},mt:{type:"spacing",property:"marginTop"},mb:{type:"spacing",property:"marginBottom"},ml:{type:"spacing",property:"marginLeft"},mr:{type:"spacing",property:"marginRight"},ms:{type:"spacing",property:"marginInlineStart"},me:{type:"spacing",property:"marginInlineEnd"},mx:{type:"spacing",property:"marginInline"},my:{type:"spacing",property:"marginBlock"},p:{type:"spacing",property:"padding"},pt:{type:"spacing",property:"paddingTop"},pb:{type:"spacing",property:"paddingBottom"},pl:{type:"spacing",property:"paddingLeft"},pr:{type:"spacing",property:"paddingRight"},ps:{type:"spacing",property:"paddingInlineStart"},pe:{type:"spacing",property:"paddingInlineEnd"},px:{type:"spacing",property:"paddingInline"},py:{type:"spacing",property:"paddingBlock"},bd:{type:"border",property:"border"},bdrs:{type:"radius",property:"borderRadius"},bg:{type:"color",property:"background"},c:{type:"textColor",property:"color"},opacity:{type:"identity",property:"opacity"},ff:{type:"fontFamily",property:"fontFamily"},fz:{type:"fontSize",property:"fontSize"},fw:{type:"identity",property:"fontWeight"},lts:{type:"size",property:"letterSpacing"},ta:{type:"identity",property:"textAlign"},lh:{type:"lineHeight",property:"lineHeight"},fs:{type:"identity",property:"fontStyle"},tt:{type:"identity",property:"textTransform"},td:{type:"identity",property:"textDecoration"},w:{type:"spacing",property:"width"},miw:{type:"spacing",property:"minWidth"},maw:{type:"spacing",property:"maxWidth"},h:{type:"spacing",property:"height"},mih:{type:"spacing",property:"minHeight"},mah:{type:"spacing",property:"maxHeight"},bgsz:{type:"size",property:"backgroundSize"},bgp:{type:"identity",property:"backgroundPosition"},bgr:{type:"identity",property:"backgroundRepeat"},bga:{type:"identity",property:"backgroundAttachment"},pos:{type:"identity",property:"position"},top:{type:"size",property:"top"},left:{type:"size",property:"left"},bottom:{type:"size",property:"bottom"},right:{type:"size",property:"right"},inset:{type:"size",property:"inset"},display:{type:"identity",property:"display"},flex:{type:"identity",property:"flex"}};function St(e,t){const r=Te({color:e,theme:t});return r.color==="dimmed"?"var(--mantine-color-dimmed)":r.color==="bright"?"var(--mantine-color-bright)":r.variable?`var(${r.variable})`:r.color}function Eo(e,t){const r=Te({color:e,theme:t});return r.isThemeColor&&r.shade===void 0?`var(--mantine-color-${r.color}-text)`:St(e,t)}function Ao(e,t){if(typeof e=="number")return b(e);if(typeof e=="string"){const[r,n,...o]=e.split(" ").filter(a=>a.trim()!=="");let s=`${b(r)}`;return n&&(s+=` ${n}`),o.length>0&&(s+=` ${St(o.join(" "),t)}`),s.trim()}return e}const Xt={text:"var(--mantine-font-family)",mono:"var(--mantine-font-family-monospace)",monospace:"var(--mantine-font-family-monospace)",heading:"var(--mantine-font-family-headings)",headings:"var(--mantine-font-family-headings)"};function Io(e){return typeof e=="string"&&e in Xt?Xt[e]:e}const Mo=["h1","h2","h3","h4","h5","h6"];function zo(e,t){return typeof e=="string"&&e in t.fontSizes?`var(--mantine-font-size-${e})`:typeof e=="string"&&Mo.includes(e)?`var(--mantine-${e}-font-size)`:typeof e=="number"||typeof e=="string"?b(e):e}function Lo(e){return e}const Bo=["h1","h2","h3","h4","h5","h6"];function Wo(e,t){return typeof e=="string"&&e in t.lineHeights?`var(--mantine-line-height-${e})`:typeof e=="string"&&Bo.includes(e)?`var(--mantine-${e}-line-height)`:e}function Ho(e,t){return typeof e=="string"&&e in t.radius?`var(--mantine-radius-${e})`:typeof e=="number"||typeof e=="string"?b(e):e}function Do(e){return typeof e=="number"?b(e):e}function Oo(e,t){if(typeof e=="number")return b(e);if(typeof e=="string"){const r=e.replace("-","");if(!(r in t.spacing))return b(e);const n=`--mantine-spacing-${r}`;return e.startsWith("-")?`calc(var(${n}) * -1)`:`var(${n})`}return e}const at={color:St,textColor:Eo,fontSize:zo,spacing:Oo,radius:Ho,identity:Lo,size:Do,lineHeight:Wo,fontFamily:Io,border:Ao};function qt(e){return e.replace("(min-width: ","").replace("em)","")}function Fo({media:e,...t}){const n=Object.keys(e).sort((o,s)=>Number(qt(o))-Number(qt(s))).map(o=>({query:o,styles:e[o]}));return{...t,media:n}}function Vo(e){if(typeof e!="object"||e===null)return!1;const t=Object.keys(e);return!(t.length===1&&t[0]==="base")}function Yo(e){return typeof e=="object"&&e!==null?"base"in e?e.base:void 0:e}function Go(e){return typeof e=="object"&&e!==null?Z(e).filter(t=>t!=="base"):[]}function Uo(e,t){return typeof e=="object"&&e!==null&&t in e?e[t]:e}function Xo({styleProps:e,data:t,theme:r}){return Fo(Z(e).reduce((n,o)=>{if(o==="hiddenFrom"||o==="visibleFrom"||o==="sx")return n;const s=t[o],a=Array.isArray(s.property)?s.property:[s.property],i=Yo(e[o]);if(!Vo(e[o]))return a.forEach(l=>{n.inlineStyles[l]=at[s.type](i,r)}),n;n.hasResponsiveStyles=!0;const c=Go(e[o]);return a.forEach(l=>{i&&(n.styles[l]=at[s.type](i,r)),c.forEach(u=>{const f=`(min-width: ${r.breakpoints[u]})`;n.media[f]={...n.media[f],[l]:at[s.type](Uo(e[o],u),r)}})}),n},{hasResponsiveStyles:!1,styles:{},inlineStyles:{},media:{}}))}function qo(){return`__m__-${p.useId().replace(/:/g,"")}`}function mr(e){return e.startsWith("data-")?e:`data-${e}`}function Jo(e){return Object.keys(e).reduce((t,r)=>{const n=e[r];return n===void 0||n===""||n===!1||n===null||(t[mr(r)]=e[r]),t},{})}function hr(e){return e?typeof e=="string"?{[mr(e)]:!0}:Array.isArray(e)?[...e].reduce((t,r)=>({...t,...hr(r)}),{}):Jo(e):null}function lt(e,t){return Array.isArray(e)?[...e].reduce((r,n)=>({...r,...lt(n,t)}),{}):typeof e=="function"?e(t):e??{}}function Qo({theme:e,style:t,vars:r,styleProps:n}){const o=lt(t,e),s=lt(r,e);return{...o,...s,...n}}const br=p.forwardRef(({component:e,style:t,__vars:r,className:n,variant:o,mod:s,size:a,hiddenFrom:i,visibleFrom:c,lightHidden:l,darkHidden:u,renderRoot:f,__size:h,...m},y)=>{const g=se(),v=e||"div",{styleProps:w,rest:$}=vt(m),R=On()?.()?.(w.sx),_=qo(),C=Xo({styleProps:w,theme:g,data:ko}),P={ref:y,style:Qo({theme:g,style:t,vars:r,styleProps:C.inlineStyles}),className:ue(n,R,{[_]:C.hasResponsiveStyles,"mantine-light-hidden":l,"mantine-dark-hidden":u,[`mantine-hidden-from-${i}`]:i,[`mantine-visible-from-${c}`]:c}),"data-variant":o,"data-size":sr(a)?void 0:a||void 0,size:h,...hr(s),...$};return d.jsxs(d.Fragment,{children:[C.hasResponsiveStyles&&d.jsx(No,{selector:`.${_}`,styles:C.styles,media:C.media}),typeof f=="function"?f(P):d.jsx(v,{...P})]})});br.displayName="@mantine/core/Box";const T=br;function yr(e){return e}function z(e){const t=p.forwardRef(e);return t.extend=yr,t.withProps=r=>{const n=p.forwardRef((o,s)=>d.jsx(t,{...r,...o,ref:s}));return n.extend=t.extend,n.displayName=`WithProps(${t.displayName})`,n},t}function re(e){const t=p.forwardRef(e);return t.withProps=r=>{const n=p.forwardRef((o,s)=>d.jsx(t,{...r,...o,ref:s}));return n.extend=t.extend,n.displayName=`WithProps(${t.displayName})`,n},t.extend=yr,t}const Zo=p.createContext({dir:"ltr",toggleDirection:()=>{},setDirection:()=>{}});function Ko(){return p.useContext(Zo)}function es(e){return p.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})},e)}const[ts,X]=Le("ScrollArea.Root component was not found in tree");function ye(e,t){const r=ce(t);We(()=>{let n=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(n),n=window.requestAnimationFrame(r)});return o.observe(e),()=>{window.cancelAnimationFrame(n),o.unobserve(e)}}},[e,r])}const rs=p.forwardRef((e,t)=>{const{style:r,...n}=e,o=X(),[s,a]=p.useState(0),[i,c]=p.useState(0),l=!!(s&&i);return ye(o.scrollbarX,()=>{const u=o.scrollbarX?.offsetHeight||0;o.onCornerHeightChange(u),c(u)}),ye(o.scrollbarY,()=>{const u=o.scrollbarY?.offsetWidth||0;o.onCornerWidthChange(u),a(u)}),l?d.jsx("div",{...n,ref:t,style:{...r,width:s,height:i}}):null}),ns=p.forwardRef((e,t)=>{const r=X(),n=!!(r.scrollbarX&&r.scrollbarY);return r.type!=="scroll"&&n?d.jsx(rs,{...e,ref:t}):null}),os={scrollHideDelay:1e3,type:"hover"},gr=p.forwardRef((e,t)=>{const{type:r,scrollHideDelay:n,scrollbars:o,getStyles:s,...a}=j("ScrollAreaRoot",os,e),[i,c]=p.useState(null),[l,u]=p.useState(null),[f,h]=p.useState(null),[m,y]=p.useState(null),[g,v]=p.useState(null),[w,$]=p.useState(0),[x,R]=p.useState(0),[_,C]=p.useState(!1),[P,S]=p.useState(!1),N=ge(t,k=>c(k));return d.jsx(ts,{value:{type:r,scrollHideDelay:n,scrollArea:i,viewport:l,onViewportChange:u,content:f,onContentChange:h,scrollbarX:m,onScrollbarXChange:y,scrollbarXEnabled:_,onScrollbarXEnabledChange:C,scrollbarY:g,onScrollbarYChange:v,scrollbarYEnabled:P,onScrollbarYEnabledChange:S,onCornerWidthChange:$,onCornerHeightChange:R,getStyles:s},children:d.jsx(T,{...a,ref:N,__vars:{"--sa-corner-width":o!=="xy"?"0px":`${w}px`,"--sa-corner-height":o!=="xy"?"0px":`${x}px`}})})});gr.displayName="@mantine/core/ScrollAreaRoot";function vr(e,t){const r=e/t;return Number.isNaN(r)?0:r}function Oe(e){const t=vr(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-r)*t;return Math.max(n,18)}function Sr(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}function ss(e,[t,r]){return Math.min(r,Math.max(t,e))}function Jt(e,t,r="ltr"){const n=Oe(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,s=t.scrollbar.size-o,a=t.content-t.viewport,i=s-n,c=r==="ltr"?[0,a]:[a*-1,0],l=ss(e,c);return Sr([0,a],[0,i])(l)}function as(e,t,r,n="ltr"){const o=Oe(r),s=o/2,a=t||s,i=o-a,c=r.scrollbar.paddingStart+a,l=r.scrollbar.size-r.scrollbar.paddingEnd-i,u=r.content-r.viewport,f=n==="ltr"?[0,u]:[u*-1,0];return Sr([c,l],f)(e)}function wr(e,t){return e>0&&e<t}function ze(e){return e?parseInt(e,10):0}function le(e,t,{checkForDefaultPrevented:r=!0}={}){return n=>{e?.(n),(r===!1||!n.defaultPrevented)&&t?.(n)}}const[is,xr]=Le("ScrollAreaScrollbar was not found in tree"),$r=p.forwardRef((e,t)=>{const{sizes:r,hasThumb:n,onThumbChange:o,onThumbPointerUp:s,onThumbPointerDown:a,onThumbPositionChange:i,onDragScroll:c,onWheelScroll:l,onResize:u,...f}=e,h=X(),[m,y]=p.useState(null),g=ge(t,S=>y(S)),v=p.useRef(null),w=p.useRef(""),{viewport:$}=h,x=r.content-r.viewport,R=ce(l),_=ce(i),C=Be(u,10),P=S=>{if(v.current){const N=S.clientX-v.current.left,k=S.clientY-v.current.top;c({x:N,y:k})}};return p.useEffect(()=>{const S=N=>{const k=N.target;m?.contains(k)&&R(N,x)};return document.addEventListener("wheel",S,{passive:!1}),()=>document.removeEventListener("wheel",S,{passive:!1})},[$,m,x,R]),p.useEffect(_,[r,_]),ye(m,C),ye(h.content,C),d.jsx(is,{value:{scrollbar:m,hasThumb:n,onThumbChange:ce(o),onThumbPointerUp:ce(s),onThumbPositionChange:_,onThumbPointerDown:ce(a)},children:d.jsx("div",{...f,ref:g,"data-mantine-scrollbar":!0,style:{position:"absolute",...f.style},onPointerDown:le(e.onPointerDown,S=>{S.preventDefault(),S.button===0&&(S.target.setPointerCapture(S.pointerId),v.current=m.getBoundingClientRect(),w.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",P(S))}),onPointerMove:le(e.onPointerMove,P),onPointerUp:le(e.onPointerUp,S=>{const N=S.target;N.hasPointerCapture(S.pointerId)&&(S.preventDefault(),N.releasePointerCapture(S.pointerId))}),onLostPointerCapture:()=>{document.body.style.webkitUserSelect=w.current,v.current=null}})})}),Cr=p.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,style:o,...s}=e,a=X(),[i,c]=p.useState(),l=p.useRef(null),u=ge(t,l,a.onScrollbarXChange);return p.useEffect(()=>{l.current&&c(getComputedStyle(l.current))},[l]),d.jsx($r,{"data-orientation":"horizontal",...s,ref:u,sizes:r,style:{...o,"--sa-thumb-width":`${Oe(r)}px`},onThumbPointerDown:f=>e.onThumbPointerDown(f.x),onDragScroll:f=>e.onDragScroll(f.x),onWheelScroll:(f,h)=>{if(a.viewport){const m=a.viewport.scrollLeft+f.deltaX;e.onWheelScroll(m),wr(m,h)&&f.preventDefault()}},onResize:()=>{l.current&&a.viewport&&i&&n({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:ze(i.paddingLeft),paddingEnd:ze(i.paddingRight)}})}})});Cr.displayName="@mantine/core/ScrollAreaScrollbarX";const Rr=p.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,style:o,...s}=e,a=X(),[i,c]=p.useState(),l=p.useRef(null),u=ge(t,l,a.onScrollbarYChange);return p.useEffect(()=>{l.current&&c(window.getComputedStyle(l.current))},[]),d.jsx($r,{...s,"data-orientation":"vertical",ref:u,sizes:r,style:{"--sa-thumb-height":`${Oe(r)}px`,...o},onThumbPointerDown:f=>e.onThumbPointerDown(f.y),onDragScroll:f=>e.onDragScroll(f.y),onWheelScroll:(f,h)=>{if(a.viewport){const m=a.viewport.scrollTop+f.deltaY;e.onWheelScroll(m),wr(m,h)&&f.preventDefault()}},onResize:()=>{l.current&&a.viewport&&i&&n({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:ze(i.paddingTop),paddingEnd:ze(i.paddingBottom)}})}})});Rr.displayName="@mantine/core/ScrollAreaScrollbarY";const Fe=p.forwardRef((e,t)=>{const{orientation:r="vertical",...n}=e,{dir:o}=Ko(),s=X(),a=p.useRef(null),i=p.useRef(0),[c,l]=p.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=vr(c.viewport,c.content),f={...n,sizes:c,onSizesChange:l,hasThumb:u>0&&u<1,onThumbChange:m=>{a.current=m},onThumbPointerUp:()=>{i.current=0},onThumbPointerDown:m=>{i.current=m}},h=(m,y)=>as(m,i.current,c,y);return r==="horizontal"?d.jsx(Cr,{...f,ref:t,onThumbPositionChange:()=>{if(s.viewport&&a.current){const m=s.viewport.scrollLeft,y=Jt(m,c,o);a.current.style.transform=`translate3d(${y}px, 0, 0)`}},onWheelScroll:m=>{s.viewport&&(s.viewport.scrollLeft=m)},onDragScroll:m=>{s.viewport&&(s.viewport.scrollLeft=h(m,o))}}):r==="vertical"?d.jsx(Rr,{...f,ref:t,onThumbPositionChange:()=>{if(s.viewport&&a.current){const m=s.viewport.scrollTop,y=Jt(m,c);c.scrollbar.size===0?a.current.style.setProperty("--thumb-opacity","0"):a.current.style.setProperty("--thumb-opacity","1"),a.current.style.transform=`translate3d(0, ${y}px, 0)`}},onWheelScroll:m=>{s.viewport&&(s.viewport.scrollTop=m)},onDragScroll:m=>{s.viewport&&(s.viewport.scrollTop=h(m))}}):null});Fe.displayName="@mantine/core/ScrollAreaScrollbarVisible";const wt=p.forwardRef((e,t)=>{const r=X(),{forceMount:n,...o}=e,[s,a]=p.useState(!1),i=e.orientation==="horizontal",c=Be(()=>{if(r.viewport){const l=r.viewport.offsetWidth<r.viewport.scrollWidth,u=r.viewport.offsetHeight<r.viewport.scrollHeight;a(i?l:u)}},10);return ye(r.viewport,c),ye(r.content,c),n||s?d.jsx(Fe,{"data-state":s?"visible":"hidden",...o,ref:t}):null});wt.displayName="@mantine/core/ScrollAreaScrollbarAuto";const Pr=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,o=X(),[s,a]=p.useState(!1);return p.useEffect(()=>{const{scrollArea:i}=o;let c=0;if(i){const l=()=>{window.clearTimeout(c),a(!0)},u=()=>{c=window.setTimeout(()=>a(!1),o.scrollHideDelay)};return i.addEventListener("pointerenter",l),i.addEventListener("pointerleave",u),()=>{window.clearTimeout(c),i.removeEventListener("pointerenter",l),i.removeEventListener("pointerleave",u)}}},[o.scrollArea,o.scrollHideDelay]),r||s?d.jsx(wt,{"data-state":s?"visible":"hidden",...n,ref:t}):null});Pr.displayName="@mantine/core/ScrollAreaScrollbarHover";const cs=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,o=X(),s=e.orientation==="horizontal",[a,i]=p.useState("hidden"),c=Be(()=>i("idle"),100);return p.useEffect(()=>{if(a==="idle"){const l=window.setTimeout(()=>i("hidden"),o.scrollHideDelay);return()=>window.clearTimeout(l)}},[a,o.scrollHideDelay]),p.useEffect(()=>{const{viewport:l}=o,u=s?"scrollLeft":"scrollTop";if(l){let f=l[u];const h=()=>{const m=l[u];f!==m&&(i("scrolling"),c()),f=m};return l.addEventListener("scroll",h),()=>l.removeEventListener("scroll",h)}},[o.viewport,s,c]),r||a!=="hidden"?d.jsx(Fe,{"data-state":a==="hidden"?"hidden":"visible",...n,ref:t,onPointerEnter:le(e.onPointerEnter,()=>i("interacting")),onPointerLeave:le(e.onPointerLeave,()=>i("idle"))}):null}),dt=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,o=X(),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:a}=o,i=e.orientation==="horizontal";return p.useEffect(()=>(i?s(!0):a(!0),()=>{i?s(!1):a(!1)}),[i,s,a]),o.type==="hover"?d.jsx(Pr,{...n,ref:t,forceMount:r}):o.type==="scroll"?d.jsx(cs,{...n,ref:t,forceMount:r}):o.type==="auto"?d.jsx(wt,{...n,ref:t,forceMount:r}):o.type==="always"?d.jsx(Fe,{...n,ref:t}):null});dt.displayName="@mantine/core/ScrollAreaScrollbar";function ls(e,t=()=>{}){let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){const s={left:e.scrollLeft,top:e.scrollTop},a=r.left!==s.left,i=r.top!==s.top;(a||i)&&t(),r=s,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)}const Tr=p.forwardRef((e,t)=>{const{style:r,...n}=e,o=X(),s=xr(),{onThumbPositionChange:a}=s,i=ge(t,u=>s.onThumbChange(u)),c=p.useRef(void 0),l=Be(()=>{c.current&&(c.current(),c.current=void 0)},100);return p.useEffect(()=>{const{viewport:u}=o;if(u){const f=()=>{if(l(),!c.current){const h=ls(u,a);c.current=h,a()}};return a(),u.addEventListener("scroll",f),()=>u.removeEventListener("scroll",f)}},[o.viewport,l,a]),d.jsx("div",{"data-state":s.hasThumb?"visible":"hidden",...n,ref:i,style:{width:"var(--sa-thumb-width)",height:"var(--sa-thumb-height)",...r},onPointerDownCapture:le(e.onPointerDownCapture,u=>{const h=u.target.getBoundingClientRect(),m=u.clientX-h.left,y=u.clientY-h.top;s.onThumbPointerDown({x:m,y})}),onPointerUp:le(e.onPointerUp,s.onThumbPointerUp)})});Tr.displayName="@mantine/core/ScrollAreaThumb";const ut=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,o=xr();return r||o.hasThumb?d.jsx(Tr,{ref:t,...n}):null});ut.displayName="@mantine/core/ScrollAreaThumb";const _r=p.forwardRef(({children:e,style:t,...r},n)=>{const o=X(),s=ge(n,o.onViewportChange);return d.jsx(T,{...r,ref:s,style:{overflowX:o.scrollbarXEnabled?"scroll":"hidden",overflowY:o.scrollbarYEnabled?"scroll":"hidden",...t},children:d.jsx("div",{...o.getStyles("content"),ref:o.onContentChange,children:e})})});_r.displayName="@mantine/core/ScrollAreaViewport";var xt={root:"m_d57069b5",viewport:"m_c0783ff9",viewportInner:"m_f8f631dd",scrollbar:"m_c44ba933",thumb:"m_d8b5e363",corner:"m_21657268",content:"m_b1336c6"};const jr={scrollHideDelay:1e3,type:"hover",scrollbars:"xy"},ds=(e,{scrollbarSize:t,overscrollBehavior:r})=>({root:{"--scrollarea-scrollbar-size":b(t),"--scrollarea-over-scroll-behavior":r}}),_e=z((e,t)=>{const r=j("ScrollArea",jr,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,scrollbarSize:c,vars:l,type:u,scrollHideDelay:f,viewportProps:h,viewportRef:m,onScrollPositionChange:y,children:g,offsetScrollbars:v,scrollbars:w,onBottomReached:$,onTopReached:x,overscrollBehavior:R,..._}=r,[C,P]=p.useState(!1),[S,N]=p.useState(!1),[k,D]=p.useState(!1),I=E({name:"ScrollArea",props:r,classes:xt,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:l,varsResolver:ds}),M=p.useRef(null),B=es([m,M]);return p.useEffect(()=>{if(!M.current||v!=="present")return;const W=M.current,L=new ResizeObserver(()=>{const{scrollHeight:Y,clientHeight:O,scrollWidth:G,clientWidth:H}=W;N(Y>O),D(G>H)});return L.observe(W),()=>L.disconnect()},[M,v]),d.jsxs(gr,{getStyles:I,type:u==="never"?"always":u,scrollHideDelay:f,ref:t,scrollbars:w,...I("root"),..._,children:[d.jsx(_r,{...h,...I("viewport",{style:h?.style}),ref:B,"data-offset-scrollbars":v===!0?"xy":v||void 0,"data-scrollbars":w||void 0,"data-horizontal-hidden":v==="present"&&!k?"true":void 0,"data-vertical-hidden":v==="present"&&!S?"true":void 0,onScroll:W=>{h?.onScroll?.(W),y?.({x:W.currentTarget.scrollLeft,y:W.currentTarget.scrollTop});const{scrollTop:L,scrollHeight:Y,clientHeight:O}=W.currentTarget;L-(Y-O)>=-.6&&$?.(),L===0&&x?.()},children:g}),(w==="xy"||w==="x")&&d.jsx(dt,{...I("scrollbar"),orientation:"horizontal","data-hidden":u==="never"||v==="present"&&!k?!0:void 0,forceMount:!0,onMouseEnter:()=>P(!0),onMouseLeave:()=>P(!1),children:d.jsx(ut,{...I("thumb")})}),(w==="xy"||w==="y")&&d.jsx(dt,{...I("scrollbar"),orientation:"vertical","data-hidden":u==="never"||v==="present"&&!S?!0:void 0,forceMount:!0,onMouseEnter:()=>P(!0),onMouseLeave:()=>P(!1),children:d.jsx(ut,{...I("thumb")})}),d.jsx(ns,{...I("corner"),"data-hovered":C||void 0,"data-hidden":u==="never"||void 0})]})});_e.displayName="@mantine/core/ScrollArea";const $t=z((e,t)=>{const{children:r,classNames:n,styles:o,scrollbarSize:s,scrollHideDelay:a,type:i,dir:c,offsetScrollbars:l,viewportRef:u,onScrollPositionChange:f,unstyled:h,variant:m,viewportProps:y,scrollbars:g,style:v,vars:w,onBottomReached:$,onTopReached:x,...R}=j("ScrollAreaAutosize",jr,e);return d.jsx(T,{...R,ref:t,style:[{display:"flex",overflow:"auto"},v],children:d.jsx(T,{style:{display:"flex",flexDirection:"column",flex:1},children:d.jsx(_e,{classNames:n,styles:o,scrollHideDelay:a,scrollbarSize:s,type:i,dir:c,offsetScrollbars:l,viewportRef:u,onScrollPositionChange:f,unstyled:h,variant:m,viewportProps:y,vars:w,scrollbars:g,onBottomReached:$,onTopReached:x,children:r})})})});_e.classes=xt;$t.displayName="@mantine/core/ScrollAreaAutosize";$t.classes=xt;_e.Autosize=$t;var Nr={root:"m_87cf2631"};const us={__staticSelector:"UnstyledButton"},Ve=re((e,t)=>{const r=j("UnstyledButton",us,e),{className:n,component:o="button",__staticSelector:s,unstyled:a,classNames:i,styles:c,style:l,...u}=r,f=E({name:s,props:r,classes:Nr,className:n,style:l,classNames:i,styles:c,unstyled:a});return d.jsx(T,{...f("root",{focusable:!0}),component:o,ref:t,type:o==="button"?"button":void 0,...u})});Ve.classes=Nr;Ve.displayName="@mantine/core/UnstyledButton";var kr={root:"m_1b7284a3"};const fs={},ps=(e,{radius:t,shadow:r})=>({root:{"--paper-radius":t===void 0?void 0:de(t),"--paper-shadow":Pn(r)}}),Ct=re((e,t)=>{const r=j("Paper",fs,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,withBorder:c,vars:l,radius:u,shadow:f,variant:h,mod:m,...y}=r,g=E({name:"Paper",props:r,classes:kr,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:l,varsResolver:ps});return d.jsx(T,{ref:t,mod:[{"data-with-border":c},m],...g("root"),variant:h,...y})});Ct.classes=kr;Ct.displayName="@mantine/core/Paper";const Re=e=>({in:{opacity:1,transform:"scale(1)"},out:{opacity:0,transform:`scale(.9) translateY(${e==="bottom"?10:-10}px)`},transitionProperty:"transform, opacity"}),Ae={fade:{in:{opacity:1},out:{opacity:0},transitionProperty:"opacity"},"fade-up":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(30px)"},transitionProperty:"opacity, transform"},"fade-down":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(-30px)"},transitionProperty:"opacity, transform"},"fade-left":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(30px)"},transitionProperty:"opacity, transform"},"fade-right":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(-30px)"},transitionProperty:"opacity, transform"},scale:{in:{opacity:1,transform:"scale(1)"},out:{opacity:0,transform:"scale(0)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"scale-y":{in:{opacity:1,transform:"scaleY(1)"},out:{opacity:0,transform:"scaleY(0)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"scale-x":{in:{opacity:1,transform:"scaleX(1)"},out:{opacity:0,transform:"scaleX(0)"},common:{transformOrigin:"left"},transitionProperty:"transform, opacity"},"skew-up":{in:{opacity:1,transform:"translateY(0) skew(0deg, 0deg)"},out:{opacity:0,transform:"translateY(-20px) skew(-10deg, -5deg)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"skew-down":{in:{opacity:1,transform:"translateY(0) skew(0deg, 0deg)"},out:{opacity:0,transform:"translateY(20px) skew(-10deg, -5deg)"},common:{transformOrigin:"bottom"},transitionProperty:"transform, opacity"},"rotate-left":{in:{opacity:1,transform:"translateY(0) rotate(0deg)"},out:{opacity:0,transform:"translateY(20px) rotate(-5deg)"},common:{transformOrigin:"bottom"},transitionProperty:"transform, opacity"},"rotate-right":{in:{opacity:1,transform:"translateY(0) rotate(0deg)"},out:{opacity:0,transform:"translateY(20px) rotate(5deg)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"slide-down":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(-100%)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"slide-up":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(100%)"},common:{transformOrigin:"bottom"},transitionProperty:"transform, opacity"},"slide-left":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(100%)"},common:{transformOrigin:"left"},transitionProperty:"transform, opacity"},"slide-right":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(-100%)"},common:{transformOrigin:"right"},transitionProperty:"transform, opacity"},pop:{...Re("bottom"),common:{transformOrigin:"center center"}},"pop-bottom-left":{...Re("bottom"),common:{transformOrigin:"bottom left"}},"pop-bottom-right":{...Re("bottom"),common:{transformOrigin:"bottom right"}},"pop-top-left":{...Re("top"),common:{transformOrigin:"top left"}},"pop-top-right":{...Re("top"),common:{transformOrigin:"top right"}}},Qt={entering:"in",entered:"in",exiting:"out",exited:"out","pre-exiting":"out","pre-entering":"out"};function ms({transition:e,state:t,duration:r,timingFunction:n}){const o={WebkitBackfaceVisibility:"hidden",willChange:"transform, opacity",transitionDuration:`${r}ms`,transitionTimingFunction:n};return typeof e=="string"?e in Ae?{transitionProperty:Ae[e].transitionProperty,...o,...Ae[e].common,...Ae[e][Qt[t]]}:{}:{transitionProperty:e.transitionProperty,...o,...e.common,...e[Qt[t]]}}function hs({duration:e,exitDuration:t,timingFunction:r,mounted:n,onEnter:o,onExit:s,onEntered:a,onExited:i,enterDelay:c,exitDelay:l}){const u=se(),f=Mn(),h=u.respectReducedMotion?f:!1,[m,y]=p.useState(h?0:e),[g,v]=p.useState(n?"entered":"exited"),w=p.useRef(-1),$=p.useRef(-1),x=p.useRef(-1);function R(){window.clearTimeout(w.current),window.clearTimeout($.current),cancelAnimationFrame(x.current)}const _=P=>{R();const S=P?o:s,N=P?a:i,k=h?0:P?e:t;y(k),k===0?(typeof S=="function"&&S(),typeof N=="function"&&N(),v(P?"entered":"exited")):x.current=requestAnimationFrame(()=>{gn.flushSync(()=>{v(P?"pre-entering":"pre-exiting")}),x.current=requestAnimationFrame(()=>{typeof S=="function"&&S(),v(P?"entering":"exiting"),w.current=window.setTimeout(()=>{typeof N=="function"&&N(),v(P?"entered":"exited")},k)})})},C=P=>{if(R(),typeof(P?c:l)!="number"){_(P);return}$.current=window.setTimeout(()=>{_(P)},P?c:l)};return kn(()=>{C(n)},[n]),p.useEffect(()=>()=>{R()},[]),{transitionDuration:m,transitionStatus:g,transitionTimingFunction:r||"ease"}}function Er({keepMounted:e,transition:t="fade",duration:r=250,exitDuration:n=r,mounted:o,children:s,timingFunction:a="ease",onExit:i,onEntered:c,onEnter:l,onExited:u,enterDelay:f,exitDelay:h}){const m=Vn(),{transitionDuration:y,transitionStatus:g,transitionTimingFunction:v}=hs({mounted:o,exitDuration:n,duration:r,timingFunction:a,onExit:i,onEntered:c,onEnter:l,onExited:u,enterDelay:f,exitDelay:h});return y===0||m==="test"?o?d.jsx(d.Fragment,{children:s({})}):e?s({display:"none"}):null:g==="exited"?e?s({display:"none"}):null:d.jsx(d.Fragment,{children:s(ms({transition:t,duration:y,state:g,timingFunction:v}))})}Er.displayName="@mantine/core/Transition";var J={root:"m_5ae2e3c",barsLoader:"m_7a2bd4cd",bar:"m_870bb79","bars-loader-animation":"m_5d2b3b9d",dotsLoader:"m_4e3f22d7",dot:"m_870c4af","loader-dots-animation":"m_aac34a1",ovalLoader:"m_b34414df","oval-loader-animation":"m_f8e89c4b"};const Ar=p.forwardRef(({className:e,...t},r)=>d.jsxs(T,{component:"span",className:ue(J.barsLoader,e),...t,ref:r,children:[d.jsx("span",{className:J.bar}),d.jsx("span",{className:J.bar}),d.jsx("span",{className:J.bar})]}));Ar.displayName="@mantine/core/Bars";const Ir=p.forwardRef(({className:e,...t},r)=>d.jsxs(T,{component:"span",className:ue(J.dotsLoader,e),...t,ref:r,children:[d.jsx("span",{className:J.dot}),d.jsx("span",{className:J.dot}),d.jsx("span",{className:J.dot})]}));Ir.displayName="@mantine/core/Dots";const Mr=p.forwardRef(({className:e,...t},r)=>d.jsx(T,{component:"span",className:ue(J.ovalLoader,e),...t,ref:r}));Mr.displayName="@mantine/core/Oval";const zr={bars:Ar,oval:Mr,dots:Ir},bs={loaders:zr,type:"oval"},ys=(e,{size:t,color:r})=>({root:{"--loader-size":A(t,"loader-size"),"--loader-color":r?te(r,e):void 0}}),Ye=z((e,t)=>{const r=j("Loader",bs,e),{size:n,color:o,type:s,vars:a,className:i,style:c,classNames:l,styles:u,unstyled:f,loaders:h,variant:m,children:y,...g}=r,v=E({name:"Loader",props:r,classes:J,className:i,style:c,classNames:l,styles:u,unstyled:f,vars:a,varsResolver:ys});return y?d.jsx(T,{...v("root"),ref:t,...g,children:y}):d.jsx(T,{...v("root"),ref:t,component:h[s],variant:m,size:n,...g})});Ye.defaultLoaders=zr;Ye.classes=J;Ye.displayName="@mantine/core/Loader";const Lr=p.forwardRef(({size:e="var(--cb-icon-size, 70%)",style:t,...r},n)=>d.jsx("svg",{viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{...t,width:e,height:e},ref:n,...r,children:d.jsx("path",{d:"M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})}));Lr.displayName="@mantine/core/CloseIcon";var Br={root:"m_86a44da5","root--subtle":"m_220c80f2"};const gs={variant:"subtle"},vs=(e,{size:t,radius:r,iconSize:n})=>({root:{"--cb-size":A(t,"cb-size"),"--cb-radius":r===void 0?void 0:de(r),"--cb-icon-size":b(n)}}),Rt=re((e,t)=>{const r=j("CloseButton",gs,e),{iconSize:n,children:o,vars:s,radius:a,className:i,classNames:c,style:l,styles:u,unstyled:f,"data-disabled":h,disabled:m,variant:y,icon:g,mod:v,__staticSelector:w,...$}=r,x=E({name:w||"CloseButton",props:r,className:i,style:l,classes:Br,classNames:c,styles:u,unstyled:f,vars:s,varsResolver:vs});return d.jsxs(Ve,{ref:t,...$,unstyled:f,variant:y,disabled:m,mod:[{disabled:m||h},v],...x("root",{variant:y,active:!m&&!h}),children:[g||d.jsx(Lr,{}),o]})});Rt.classes=Br;Rt.displayName="@mantine/core/CloseButton";function Ss(e){return p.Children.toArray(e).filter(Boolean)}var Wr={root:"m_4081bf90"};const ws={preventGrowOverflow:!0,gap:"md",align:"center",justify:"flex-start",wrap:"wrap"},xs=(e,{grow:t,preventGrowOverflow:r,gap:n,align:o,justify:s,wrap:a},{childWidth:i})=>({root:{"--group-child-width":t&&r?i:void 0,"--group-gap":be(n),"--group-align":o,"--group-justify":s,"--group-wrap":a}}),Hr=z((e,t)=>{const r=j("Group",ws,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,children:c,gap:l,align:u,justify:f,wrap:h,grow:m,preventGrowOverflow:y,vars:g,variant:v,__size:w,mod:$,...x}=r,R=Ss(c),_=R.length,C=be(l??"md"),S={childWidth:`calc(${100/_}% - (${C} - ${C} / ${_}))`},N=E({name:"Group",props:r,stylesCtx:S,className:o,style:s,classes:Wr,classNames:n,styles:a,unstyled:i,vars:g,varsResolver:xs});return d.jsx(T,{...N("root"),ref:t,variant:v,mod:[{grow:m},$],size:w,...x,children:R})});Hr.classes=Wr;Hr.displayName="@mantine/core/Group";const[$s,Cs]=ar({size:"sm"}),Rs={},Dr=z((e,t)=>{const r=j("InputClearButton",Rs,e),{size:n,variant:o,vars:s,classNames:a,styles:i,...c}=r,l=Cs(),{resolvedClassNames:u,resolvedStyles:f}=mo({classNames:a,styles:i,props:r});return d.jsx(Rt,{variant:o||"transparent",ref:t,size:n||l?.size||"sm",classNames:u,styles:f,__staticSelector:"InputClearButton",...c})});Dr.displayName="@mantine/core/InputClearButton";const[Ps,je]=ar({offsetBottom:!1,offsetTop:!1,describedBy:void 0,getStyles:null,inputId:void 0,labelId:void 0});var q={wrapper:"m_6c018570",input:"m_8fb7ebe7",section:"m_82577fc2",placeholder:"m_88bacfd0",root:"m_46b77525",label:"m_8fdc1311",required:"m_78a94662",error:"m_8f816625",description:"m_fe47ce59"};const Zt={},Ts=(e,{size:t})=>({description:{"--input-description-size":t===void 0?void 0:`calc(${V(t)} - ${b(2)})`}}),Ge=z((e,t)=>{const r=j("InputDescription",Zt,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,size:l,__staticSelector:u,__inheritStyles:f=!0,variant:h,...m}=j("InputDescription",Zt,r),y=je(),g=E({name:["InputWrapper",u],props:r,classes:q,className:o,style:s,classNames:n,styles:a,unstyled:i,rootSelector:"description",vars:c,varsResolver:Ts}),v=f&&y?.getStyles||g;return d.jsx(T,{component:"p",ref:t,variant:h,size:l,...v("description",y?.getStyles?{className:o,style:s}:void 0),...m})});Ge.classes=q;Ge.displayName="@mantine/core/InputDescription";const _s={},js=(e,{size:t})=>({error:{"--input-error-size":t===void 0?void 0:`calc(${V(t)} - ${b(2)})`}}),Ue=z((e,t)=>{const r=j("InputError",_s,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,size:l,__staticSelector:u,__inheritStyles:f=!0,variant:h,...m}=r,y=E({name:["InputWrapper",u],props:r,classes:q,className:o,style:s,classNames:n,styles:a,unstyled:i,rootSelector:"error",vars:c,varsResolver:js}),g=je(),v=f&&g?.getStyles||y;return d.jsx(T,{component:"p",ref:t,variant:h,size:l,...v("error",g?.getStyles?{className:o,style:s}:void 0),...m})});Ue.classes=q;Ue.displayName="@mantine/core/InputError";const Kt={labelElement:"label"},Ns=(e,{size:t})=>({label:{"--input-label-size":V(t),"--input-asterisk-color":void 0}}),Xe=z((e,t)=>{const r=j("InputLabel",Kt,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,labelElement:l,size:u,required:f,htmlFor:h,onMouseDown:m,children:y,__staticSelector:g,variant:v,mod:w,...$}=j("InputLabel",Kt,r),x=E({name:["InputWrapper",g],props:r,classes:q,className:o,style:s,classNames:n,styles:a,unstyled:i,rootSelector:"label",vars:c,varsResolver:Ns}),R=je(),_=R?.getStyles||x;return d.jsxs(T,{..._("label",R?.getStyles?{className:o,style:s}:void 0),component:l,variant:v,size:u,ref:t,htmlFor:l==="label"?h:void 0,mod:[{required:f},w],onMouseDown:C=>{m?.(C),!C.defaultPrevented&&C.detail>1&&C.preventDefault()},...$,children:[y,f&&d.jsx("span",{..._("required"),"aria-hidden":!0,children:" *"})]})});Xe.classes=q;Xe.displayName="@mantine/core/InputLabel";const er={},Pt=z((e,t)=>{const r=j("InputPlaceholder",er,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,__staticSelector:l,variant:u,error:f,mod:h,...m}=j("InputPlaceholder",er,r),y=E({name:["InputPlaceholder",l],props:r,classes:q,className:o,style:s,classNames:n,styles:a,unstyled:i,rootSelector:"placeholder"});return d.jsx(T,{...y("placeholder"),mod:[{error:!!f},h],component:"span",variant:u,ref:t,...m})});Pt.classes=q;Pt.displayName="@mantine/core/InputPlaceholder";function ks(e,{hasDescription:t,hasError:r}){const n=e.findIndex(c=>c==="input"),o=e.slice(0,n),s=e.slice(n+1),a=t&&o.includes("description")||r&&o.includes("error");return{offsetBottom:t&&s.includes("description")||r&&s.includes("error"),offsetTop:a}}const Es={labelElement:"label",inputContainer:e=>e,inputWrapperOrder:["label","description","input","error"]},As=(e,{size:t})=>({label:{"--input-label-size":V(t),"--input-asterisk-color":void 0},error:{"--input-error-size":t===void 0?void 0:`calc(${V(t)} - ${b(2)})`},description:{"--input-description-size":t===void 0?void 0:`calc(${V(t)} - ${b(2)})`}}),Tt=z((e,t)=>{const r=j("InputWrapper",Es,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,size:l,variant:u,__staticSelector:f,inputContainer:h,inputWrapperOrder:m,label:y,error:g,description:v,labelProps:w,descriptionProps:$,errorProps:x,labelElement:R,children:_,withAsterisk:C,id:P,required:S,__stylesApiProps:N,mod:k,...D}=r,I=E({name:["InputWrapper",f],props:N||r,classes:q,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:c,varsResolver:As}),M={size:l,variant:u,__staticSelector:f},B=cr(P),W=typeof C=="boolean"?C:S,L=x?.id||`${B}-error`,Y=$?.id||`${B}-description`,O=B,G=!!g&&typeof g!="boolean",H=!!v,K=`${G?L:""} ${H?Y:""}`,ae=K.trim().length>0?K.trim():void 0,F=w?.id||`${B}-label`,Se=y&&d.jsx(Xe,{labelElement:R,id:F,htmlFor:O,required:W,...M,...w,children:y},"label"),ne=H&&d.jsx(Ge,{...$,...M,size:$?.size||M.size,id:$?.id||Y,children:v},"description"),we=d.jsx(p.Fragment,{children:h(_)},"input"),ie=G&&p.createElement(Ue,{...x,...M,size:x?.size||M.size,key:"error",id:x?.id||L},g),Qe=m.map(Ze=>{switch(Ze){case"label":return Se;case"input":return we;case"description":return ne;case"error":return ie;default:return null}});return d.jsx(Ps,{value:{getStyles:I,describedBy:ae,inputId:O,labelId:F,...ks(m,{hasDescription:H,hasError:G})},children:d.jsx(T,{ref:t,variant:u,size:l,mod:[{error:!!g},k],...I("root"),...D,children:Qe})})});Tt.classes=q;Tt.displayName="@mantine/core/InputWrapper";const Is={variant:"default",leftSectionPointerEvents:"none",rightSectionPointerEvents:"none",withAria:!0,withErrorStyles:!0},Ms=(e,t,r)=>({wrapper:{"--input-margin-top":r.offsetTop?"calc(var(--mantine-spacing-xs) / 2)":void 0,"--input-margin-bottom":r.offsetBottom?"calc(var(--mantine-spacing-xs) / 2)":void 0,"--input-height":A(t.size,"input-height"),"--input-fz":V(t.size),"--input-radius":t.radius===void 0?void 0:de(t.radius),"--input-left-section-width":t.leftSectionWidth!==void 0?b(t.leftSectionWidth):void 0,"--input-right-section-width":t.rightSectionWidth!==void 0?b(t.rightSectionWidth):void 0,"--input-padding-y":t.multiline?A(t.size,"input-padding-y"):void 0,"--input-left-section-pointer-events":t.leftSectionPointerEvents,"--input-right-section-pointer-events":t.rightSectionPointerEvents}}),U=re((e,t)=>{const r=j("Input",Is,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,required:c,__staticSelector:l,__stylesApiProps:u,size:f,wrapperProps:h,error:m,disabled:y,leftSection:g,leftSectionProps:v,leftSectionWidth:w,rightSection:$,rightSectionProps:x,rightSectionWidth:R,rightSectionPointerEvents:_,leftSectionPointerEvents:C,variant:P,vars:S,pointer:N,multiline:k,radius:D,id:I,withAria:M,withErrorStyles:B,mod:W,inputSize:L,__clearSection:Y,__clearable:O,__defaultRightSection:G,...H}=r,{styleProps:K,rest:ae}=vt(H),F=je(),Se={offsetBottom:F?.offsetBottom,offsetTop:F?.offsetTop},ne=E({name:["Input",l],props:u||r,classes:q,className:o,style:s,classNames:n,styles:a,unstyled:i,stylesCtx:Se,rootSelector:"wrapper",vars:S,varsResolver:Ms}),we=M?{required:c,disabled:y,"aria-invalid":!!m,"aria-describedby":F?.describedBy,id:F?.inputId||I}:{},ie=$||O&&Y||G;return d.jsx($s,{value:{size:f||"sm"},children:d.jsxs(T,{...ne("wrapper"),...K,...h,mod:[{error:!!m&&B,pointer:N,disabled:y,multiline:k,"data-with-right-section":!!ie,"data-with-left-section":!!g},W],variant:P,size:f,children:[g&&d.jsx("div",{...v,"data-position":"left",...ne("section",{className:v?.className,style:v?.style}),children:g}),d.jsx(T,{component:"input",...ae,...we,ref:t,required:c,mod:{disabled:y,error:!!m&&B},variant:P,__size:L,...ne("input")}),ie&&d.jsx("div",{...x,"data-position":"right",...ne("section",{className:x?.className,style:x?.style}),children:ie})]})})});U.classes=q;U.Wrapper=Tt;U.Label=Xe;U.Error=Ue;U.Description=Ge;U.Placeholder=Pt;U.ClearButton=Dr;U.displayName="@mantine/core/Input";var Or={root:"m_b6d8b162"};function zs(e){if(e==="start")return"start";if(e==="end"||e)return"end"}const Ls={inherit:!1},Bs=(e,{variant:t,lineClamp:r,gradient:n,size:o,color:s})=>({root:{"--text-fz":V(o),"--text-lh":Rn(o),"--text-gradient":t==="gradient"?it(n,e):void 0,"--text-line-clamp":typeof r=="number"?r.toString():void 0,"--text-color":s?te(s,e):void 0}}),Fr=re((e,t)=>{const r=j("Text",Ls,e),{lineClamp:n,truncate:o,inline:s,inherit:a,gradient:i,span:c,__staticSelector:l,vars:u,className:f,style:h,classNames:m,styles:y,unstyled:g,variant:v,mod:w,size:$,...x}=r,R=E({name:["Text",l],props:r,classes:Or,className:f,style:h,classNames:m,styles:y,unstyled:g,vars:u,varsResolver:Bs});return d.jsx(T,{...R("root",{focusable:!0}),ref:t,component:c?"span":"p",variant:v,mod:[{"data-truncate":zs(o),"data-line-clamp":typeof n=="number","data-inline":s,"data-inherit":a},w],size:$,...x})});Fr.classes=Or;Fr.displayName="@mantine/core/Text";var Vr={root:"m_5f75b09e",body:"m_5f6e695e",labelWrapper:"m_d3ea56bb",label:"m_8ee546b8",description:"m_328f68c0",error:"m_8e8a99cc"};const Ws=Vr,Yr=p.forwardRef(({__staticSelector:e,__stylesApiProps:t,className:r,classNames:n,styles:o,unstyled:s,children:a,label:i,description:c,id:l,disabled:u,error:f,size:h,labelPosition:m="left",bodyElement:y="div",labelElement:g="label",variant:v,style:w,vars:$,mod:x,...R},_)=>{const C=E({name:e,props:t,className:r,style:w,classes:Vr,classNames:n,styles:o,unstyled:s});return d.jsx(T,{...C("root"),ref:_,__vars:{"--label-fz":V(h),"--label-lh":A(h,"label-lh")},mod:[{"label-position":m},x],variant:v,size:h,...R,children:d.jsxs(T,{component:y,htmlFor:y==="label"?l:void 0,...C("body"),children:[a,d.jsxs("div",{...C("labelWrapper"),"data-disabled":u||void 0,children:[i&&d.jsx(T,{component:g,htmlFor:g==="label"?l:void 0,...C("label"),"data-disabled":u||void 0,children:i}),c&&d.jsx(U.Description,{size:h,__inheritStyles:!1,...C("description"),children:c}),f&&typeof f!="boolean"&&d.jsx(U.Error,{size:h,__inheritStyles:!1,...C("error"),children:f})]})]})})});Yr.displayName="@mantine/core/InlineInput";function Hs({children:e,role:t}){const r=je();return r?d.jsx("div",{role:t,"aria-labelledby":r.labelId,"aria-describedby":r.describedBy,children:e}):d.jsx(d.Fragment,{children:e})}var Gr={root:"m_347db0ec","root--dot":"m_fbd81e3d",label:"m_5add502a",section:"m_91fdda9b"};const Ds={},Os=(e,{radius:t,color:r,gradient:n,variant:o,size:s,autoContrast:a})=>{const i=e.variantColorResolver({color:r||e.primaryColor,theme:e,gradient:n,variant:o||"filled",autoContrast:a});return{root:{"--badge-height":A(s,"badge-height"),"--badge-padding-x":A(s,"badge-padding-x"),"--badge-fz":A(s,"badge-fz"),"--badge-radius":t===void 0?void 0:de(t),"--badge-bg":r||o?i.background:void 0,"--badge-color":r||o?i.color:void 0,"--badge-bd":r||o?i.border:void 0,"--badge-dot-color":o==="dot"?te(r,e):void 0}}},Ur=re((e,t)=>{const r=j("Badge",Ds,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,radius:l,color:u,gradient:f,leftSection:h,rightSection:m,children:y,variant:g,fullWidth:v,autoContrast:w,circle:$,mod:x,...R}=r,_=E({name:"Badge",props:r,classes:Gr,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:c,varsResolver:Os});return d.jsxs(T,{variant:g,mod:[{block:v,circle:$,"with-right-section":!!m,"with-left-section":!!h},x],..._("root",{variant:g}),ref:t,...R,children:[h&&d.jsx("span",{..._("section"),"data-position":"left",children:h}),d.jsx("span",{..._("label"),children:y}),m&&d.jsx("span",{..._("section"),"data-position":"right",children:m})]})});Ur.classes=Gr;Ur.displayName="@mantine/core/Badge";var ve={root:"m_77c9d27d",inner:"m_80f1301b",label:"m_811560b9",section:"m_a74036a",loader:"m_a25b86ee",group:"m_80d6d844",groupSection:"m_70be2a01"};const tr={orientation:"horizontal"},Fs=(e,{borderWidth:t})=>({group:{"--button-border-width":b(t)}}),_t=z((e,t)=>{const r=j("ButtonGroup",tr,e),{className:n,style:o,classNames:s,styles:a,unstyled:i,orientation:c,vars:l,borderWidth:u,variant:f,mod:h,...m}=j("ButtonGroup",tr,e),y=E({name:"ButtonGroup",props:r,classes:ve,className:n,style:o,classNames:s,styles:a,unstyled:i,vars:l,varsResolver:Fs,rootSelector:"group"});return d.jsx(T,{...y("group"),ref:t,variant:f,mod:[{"data-orientation":c},h],role:"group",...m})});_t.classes=ve;_t.displayName="@mantine/core/ButtonGroup";const rr={},Vs=(e,{radius:t,color:r,gradient:n,variant:o,autoContrast:s,size:a})=>{const i=e.variantColorResolver({color:r||e.primaryColor,theme:e,gradient:n,variant:o||"filled",autoContrast:s});return{groupSection:{"--section-height":A(a,"section-height"),"--section-padding-x":A(a,"section-padding-x"),"--section-fz":a?.includes("compact")?V(a.replace("compact-","")):V(a),"--section-radius":t===void 0?void 0:de(t),"--section-bg":r||o?i.background:void 0,"--section-color":i.color,"--section-bd":r||o?i.border:void 0}}},jt=z((e,t)=>{const r=j("ButtonGroupSection",rr,e),{className:n,style:o,classNames:s,styles:a,unstyled:i,vars:c,variant:l,gradient:u,radius:f,autoContrast:h,...m}=j("ButtonGroupSection",rr,e),y=E({name:"ButtonGroupSection",props:r,classes:ve,className:n,style:o,classNames:s,styles:a,unstyled:i,vars:c,varsResolver:Vs,rootSelector:"groupSection"});return d.jsx(T,{...y("groupSection"),ref:t,variant:l,...m})});jt.classes=ve;jt.displayName="@mantine/core/ButtonGroupSection";const Ys={in:{opacity:1,transform:`translate(-50%, calc(-50% + ${b(1)}))`},out:{opacity:0,transform:"translate(-50%, -200%)"},common:{transformOrigin:"center"},transitionProperty:"transform, opacity"},Gs={},Us=(e,{radius:t,color:r,gradient:n,variant:o,size:s,justify:a,autoContrast:i})=>{const c=e.variantColorResolver({color:r||e.primaryColor,theme:e,gradient:n,variant:o||"filled",autoContrast:i});return{root:{"--button-justify":a,"--button-height":A(s,"button-height"),"--button-padding-x":A(s,"button-padding-x"),"--button-fz":s?.includes("compact")?V(s.replace("compact-","")):V(s),"--button-radius":t===void 0?void 0:de(t),"--button-bg":r||o?c.background:void 0,"--button-hover":r||o?c.hover:void 0,"--button-color":c.color,"--button-bd":r||o?c.border:void 0,"--button-hover-color":r||o?c.hoverColor:void 0}}},qe=re((e,t)=>{const r=j("Button",Gs,e),{style:n,vars:o,className:s,color:a,disabled:i,children:c,leftSection:l,rightSection:u,fullWidth:f,variant:h,radius:m,loading:y,loaderProps:g,gradient:v,classNames:w,styles:$,unstyled:x,"data-disabled":R,autoContrast:_,mod:C,...P}=r,S=E({name:"Button",props:r,classes:ve,className:s,style:n,classNames:w,styles:$,unstyled:x,vars:o,varsResolver:Us}),N=!!l,k=!!u;return d.jsxs(Ve,{ref:t,...S("root",{active:!i&&!y&&!R}),unstyled:x,variant:h,disabled:i||y,mod:[{disabled:i||R,loading:y,block:f,"with-left-section":N,"with-right-section":k},C],...P,children:[d.jsx(Er,{mounted:!!y,transition:Ys,duration:150,children:D=>d.jsx(T,{component:"span",...S("loader",{style:D}),"aria-hidden":!0,children:d.jsx(Ye,{color:"var(--button-color)",size:"calc(var(--button-height) / 1.8)",...g})})}),d.jsxs("span",{...S("inner"),children:[l&&d.jsx(T,{component:"span",...S("section"),mod:{position:"left"},children:l}),d.jsx(T,{component:"span",mod:{loading:y},...S("label"),children:c}),u&&d.jsx(T,{component:"span",...S("section"),mod:{position:"right"},children:u})]})]})});qe.classes=ve;qe.displayName="@mantine/core/Button";qe.Group=_t;qe.GroupSection=jt;const[Xs,qs]=Le("Card component was not found in tree");var Nt={root:"m_e615b15f",section:"m_599a2148"};const Js={},Je=re((e,t)=>{const r=j("CardSection",Js,e),{classNames:n,className:o,style:s,styles:a,vars:i,withBorder:c,inheritPadding:l,mod:u,...f}=r,h=qs();return d.jsx(T,{ref:t,mod:[{"with-border":c,"inherit-padding":l},u],...h.getStyles("section",{className:o,style:s,styles:a,classNames:n}),...f})});Je.classes=Nt;Je.displayName="@mantine/core/CardSection";const Qs={},Zs=(e,{padding:t})=>({root:{"--card-padding":be(t)}}),kt=re((e,t)=>{const r=j("Card",Qs,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,children:l,padding:u,...f}=r,h=E({name:"Card",props:r,classes:Nt,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:c,varsResolver:Zs}),m=p.Children.toArray(l),y=m.map((g,v)=>typeof g=="object"&&g&&"type"in g&&g.type===Je?p.cloneElement(g,{"data-first-section":v===0||void 0,"data-last-section":v===m.length-1||void 0}):g);return d.jsx(Xs,{value:{getStyles:h},children:d.jsx(Ct,{ref:t,unstyled:i,...h("root"),...f,children:y})})});kt.classes=Nt;kt.displayName="@mantine/core/Card";kt.Section=Je;function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nr.apply(null,arguments)}function ya(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Xr={root:"m_6d731127"};const Ks={gap:"md",align:"stretch",justify:"flex-start"},ea=(e,{gap:t,align:r,justify:n})=>({root:{"--stack-gap":be(t),"--stack-align":r,"--stack-justify":n}}),qr=z((e,t)=>{const r=j("Stack",Ks,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,align:l,justify:u,gap:f,variant:h,...m}=r,y=E({name:"Stack",props:r,classes:Xr,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:c,varsResolver:ea});return d.jsx(T,{ref:t,...y("root"),variant:h,...m})});qr.classes=Xr;qr.displayName="@mantine/core/Stack";const Jr=p.createContext(null),ta=Jr.Provider,ra=()=>p.useContext(Jr),na={},Et=z((e,t)=>{const{value:r,defaultValue:n,onChange:o,size:s,wrapperProps:a,children:i,readOnly:c,...l}=j("SwitchGroup",na,e),[u,f]=lr({value:r,defaultValue:n,finalValue:[],onChange:o}),h=m=>{const y=m.currentTarget.value;!c&&f(u.includes(y)?u.filter(g=>g!==y):[...u,y])};return d.jsx(ta,{value:{value:u,onChange:h,size:s},children:d.jsx(U.Wrapper,{size:s,ref:t,...a,...l,labelElement:"div",__staticSelector:"SwitchGroup",children:d.jsx(Hs,{role:"group",children:i})})})});Et.classes=U.Wrapper.classes;Et.displayName="@mantine/core/SwitchGroup";var Qr={root:"m_5f93f3bb",input:"m_926b4011",track:"m_9307d992",thumb:"m_93039a1d",trackLabel:"m_8277e082"};const oa={labelPosition:"right",withThumbIndicator:!0},sa=(e,{radius:t,color:r,size:n})=>({root:{"--switch-radius":t===void 0?void 0:de(t),"--switch-height":A(n,"switch-height"),"--switch-width":A(n,"switch-width"),"--switch-thumb-size":A(n,"switch-thumb-size"),"--switch-label-font-size":A(n,"switch-label-font-size"),"--switch-track-label-padding":A(n,"switch-track-label-padding"),"--switch-color":r?te(r,e):void 0}}),At=z((e,t)=>{const r=j("Switch",oa,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,color:l,label:u,offLabel:f,onLabel:h,id:m,size:y,radius:g,wrapperProps:v,thumbIcon:w,checked:$,defaultChecked:x,onChange:R,labelPosition:_,description:C,error:P,disabled:S,variant:N,rootRef:k,mod:D,withThumbIndicator:I,...M}=r,B=ra(),W=y||B?.size,L=E({name:"Switch",props:r,classes:Qr,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:c,varsResolver:sa}),{styleProps:Y,rest:O}=vt(M),G=cr(m),H=B?{checked:B.value.includes(O.value),onChange:B.onChange}:{},[K,ae]=lr({value:H.checked??$,defaultValue:x,finalValue:!1});return d.jsxs(Yr,{...L("root"),__staticSelector:"Switch",__stylesApiProps:r,id:G,size:W,labelPosition:_,label:u,description:C,error:P,disabled:S,bodyElement:"label",labelElement:"span",classNames:n,styles:a,unstyled:i,"data-checked":H.checked||$||void 0,variant:N,ref:k,mod:D,...Y,...v,children:[d.jsx("input",{...O,disabled:S,checked:K,"data-checked":H.checked||$||void 0,onChange:F=>{B?H.onChange?.(F):R?.(F),ae(F.currentTarget.checked)},id:G,ref:t,type:"checkbox",role:"switch",...L("input")}),d.jsxs(T,{"aria-hidden":"true",component:"span",mod:{error:P,"label-position":_,"without-labels":!h&&!f},...L("track"),children:[d.jsx(T,{component:"span",mod:{"reduce-motion":!0,"with-thumb-indicator":I&&!w},...L("thumb"),children:w}),d.jsx("span",{...L("trackLabel"),children:K?h:f})]})]})});At.classes={...Qr,...Ws};At.displayName="@mantine/core/Switch";At.Group=Et;const[aa,ia]=Le("Table component was not found in the tree");var Ne={table:"m_b23fa0ef",th:"m_4e7aa4f3",tr:"m_4e7aa4fd",td:"m_4e7aa4ef",tbody:"m_b2404537",thead:"m_b242d975",caption:"m_9e5a3ac7",scrollContainer:"m_a100c15",scrollContainerInner:"m_62259741"};function ca(e,t){if(!t)return;const r={};return t.columnBorder&&e.withColumnBorders&&(r["data-with-column-border"]=!0),t.rowBorder&&e.withRowBorders&&(r["data-with-row-border"]=!0),t.striped&&e.striped&&(r["data-striped"]=e.striped),t.highlightOnHover&&e.highlightOnHover&&(r["data-hover"]=!0),t.captionSide&&e.captionSide&&(r["data-side"]=e.captionSide),t.stickyHeader&&e.stickyHeader&&(r["data-sticky"]=!0),r}function fe(e,t){const r=`Table${e.charAt(0).toUpperCase()}${e.slice(1)}`,n=z((o,s)=>{const a=j(r,{},o),{classNames:i,className:c,style:l,styles:u,...f}=a,h=ia();return d.jsx(T,{component:e,ref:s,...ca(h,t),...h.getStyles(e,{className:c,classNames:i,style:l,styles:u,props:a}),...f})});return n.displayName=`@mantine/core/${r}`,n.classes=Ne,n}const ft=fe("th",{columnBorder:!0}),Zr=fe("td",{columnBorder:!0}),Ie=fe("tr",{rowBorder:!0,striped:!0,highlightOnHover:!0}),Kr=fe("thead",{stickyHeader:!0}),en=fe("tbody"),tn=fe("tfoot"),rn=fe("caption",{captionSide:!0});function It({data:e}){return d.jsxs(d.Fragment,{children:[e.caption&&d.jsx(rn,{children:e.caption}),e.head&&d.jsx(Kr,{children:d.jsx(Ie,{children:e.head.map((t,r)=>d.jsx(ft,{children:t},r))})}),e.body&&d.jsx(en,{children:e.body.map((t,r)=>d.jsx(Ie,{children:t.map((n,o)=>d.jsx(Zr,{children:n},o))},r))}),e.foot&&d.jsx(tn,{children:d.jsx(Ie,{children:e.foot.map((t,r)=>d.jsx(ft,{children:t},r))})})]})}It.displayName="@mantine/core/TableDataRenderer";const la={type:"scrollarea"},da=(e,{minWidth:t,maxHeight:r,type:n})=>({scrollContainer:{"--table-min-width":b(t),"--table-max-height":b(r),"--table-overflow":n==="native"?"auto":void 0}}),Mt=z((e,t)=>{const r=j("TableScrollContainer",la,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,children:l,minWidth:u,maxHeight:f,type:h,scrollAreaProps:m,...y}=r,g=E({name:"TableScrollContainer",classes:Ne,props:r,className:o,style:s,classNames:n,styles:a,unstyled:i,vars:c,varsResolver:da,rootSelector:"scrollContainer"});return d.jsx(T,{component:h==="scrollarea"?_e:"div",...h==="scrollarea"?f?{offsetScrollbars:"xy",...m}:{offsetScrollbars:"x",...m}:{},ref:t,...g("scrollContainer"),...y,children:d.jsx("div",{...g("scrollContainerInner"),children:l})})});Mt.classes=Ne;Mt.displayName="@mantine/core/TableScrollContainer";const ua={withRowBorders:!0,verticalSpacing:7},fa=(e,{layout:t,captionSide:r,horizontalSpacing:n,verticalSpacing:o,borderColor:s,stripedColor:a,highlightOnHoverColor:i,striped:c,highlightOnHover:l,stickyHeaderOffset:u,stickyHeader:f})=>({table:{"--table-layout":t,"--table-caption-side":r,"--table-horizontal-spacing":be(n),"--table-vertical-spacing":be(o),"--table-border-color":s?te(s,e):void 0,"--table-striped-color":c&&a?te(a,e):void 0,"--table-highlight-on-hover-color":l&&i?te(i,e):void 0,"--table-sticky-header-offset":f?b(u):void 0}}),Q=z((e,t)=>{const r=j("Table",ua,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,vars:c,horizontalSpacing:l,verticalSpacing:u,captionSide:f,stripedColor:h,highlightOnHoverColor:m,striped:y,highlightOnHover:g,withColumnBorders:v,withRowBorders:w,withTableBorder:$,borderColor:x,layout:R,variant:_,data:C,children:P,stickyHeader:S,stickyHeaderOffset:N,mod:k,tabularNums:D,...I}=r,M=E({name:"Table",props:r,className:o,style:s,classes:Ne,classNames:n,styles:a,unstyled:i,rootSelector:"table",vars:c,varsResolver:fa});return d.jsx(aa,{value:{getStyles:M,stickyHeader:S,striped:y===!0?"odd":y||void 0,highlightOnHover:g,withColumnBorders:v,withRowBorders:w,captionSide:f||"bottom"},children:d.jsx(T,{component:"table",variant:_,ref:t,mod:[{"data-with-table-border":$,"data-tabular-nums":D},k],...M("table"),...I,children:P||!!C&&d.jsx(It,{data:C})})})});Q.classes=Ne;Q.displayName="@mantine/core/Table";Q.Td=Zr;Q.Th=ft;Q.Tr=Ie;Q.Thead=Kr;Q.Tbody=en;Q.Tfoot=tn;Q.Caption=rn;Q.ScrollContainer=Mt;Q.DataRenderer=It;var nn={root:"m_d6493fad"};const pa={},on=z((e,t)=>{const r=j("TypographyStylesProvider",pa,e),{classNames:n,className:o,style:s,styles:a,unstyled:i,...c}=r,l=E({name:"TypographyStylesProvider",classes:nn,props:r,className:o,style:s,classNames:n,styles:a,unstyled:i});return d.jsx(T,{ref:t,...l("root"),...c})});on.classes=nn;on.displayName="@mantine/core/TypographyStylesProvider";export{Ur as B,kt as C,Hr as G,po as M,Ct as P,qr as S,on as T,ya as _,nr as a,Q as b,Fr as c,At as d,qe as e,d as j,ba as u};
