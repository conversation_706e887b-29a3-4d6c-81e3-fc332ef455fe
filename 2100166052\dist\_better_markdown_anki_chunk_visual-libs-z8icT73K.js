import{r as e}from"./_better_markdown_anki_chunk_react-vendor-Bkk_KtsK.js";/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var f={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=(n,s,i,c)=>{const o=e.forwardRef(({color:l="currentColor",size:r=24,stroke:h=2,title:a,className:p,children:t,...v},d)=>e.createElement("svg",{ref:d,...f[n],width:r,height:r,className:["tabler-icon",`tabler-icon-${s}`,p].join(" "),strokeWidth:h,stroke:l,...v},[a&&e.createElement("title",{key:"svg-title"},a),...c.map(([g,w])=>e.createElement(g,w)),...Array.isArray(t)?t:[t]]));return o.displayName=`${i}`,o};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var u=k("outline","coffee","IconCoffee",[["path",{d:"M3 14c.83 .642 2.077 1.017 3.5 1c1.423 .017 2.67 -.358 3.5 -1c.83 -.642 2.077 -1.017 3.5 -1c1.423 -.017 2.67 .358 3.5 1",key:"svg-0"}],["path",{d:"M8 3a2.4 2.4 0 0 0 -1 2a2.4 2.4 0 0 0 1 2",key:"svg-1"}],["path",{d:"M12 3a2.4 2.4 0 0 0 -1 2a2.4 2.4 0 0 0 1 2",key:"svg-2"}],["path",{d:"M3 10h14v5a6 6 0 0 1 -6 6h-2a6 6 0 0 1 -6 -6v-5z",key:"svg-3"}],["path",{d:"M16.746 16.726a3 3 0 1 0 .252 -5.555",key:"svg-4"}]]);export{u as I};
